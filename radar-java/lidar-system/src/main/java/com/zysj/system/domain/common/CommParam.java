package com.zysj.system.domain.common;

import com.zysj.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.zysj.common.annotation.Excel;

/**
 * 【请填写功能名称】对象 comm_param
 * 
 * <AUTHOR>
 * @date 2025-02-06
 */
public class CommParam extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long commParamId;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String commParamName;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String commParamVake;

    public void setCommParamId(Long commParamId) 
    {
        this.commParamId = commParamId;
    }

    public Long getCommParamId() 
    {
        return commParamId;
    }
    public void setCommParamName(String commParamName) 
    {
        this.commParamName = commParamName;
    }

    public String getCommParamName() 
    {
        return commParamName;
    }
    public void setCommParamVake(String commParamVake) 
    {
        this.commParamVake = commParamVake;
    }

    public String getCommParamVake() 
    {
        return commParamVake;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("commParamId", getCommParamId())
            .append("commParamName", getCommParamName())
            .append("commParamVake", getCommParamVake())
            .toString();
    }
}
