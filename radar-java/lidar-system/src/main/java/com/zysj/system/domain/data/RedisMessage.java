package com.zysj.system.domain.data;

import io.swagger.annotations.ApiModel;
import lombok.*;

/**
 * <AUTHOR> loso
 * @version : v1.0
 * @date : Created in 2025/2/15 10:07
 * @description :
 * @modified By : loso
 */
public class RedisMessage<T> {

    private Long ExpireTime;

    private Boolean ForceOutofDate;

    private T Value;

    public Long getExpireTime() {
        return ExpireTime;
    }

    public void setExpireTime(Long expireTime) {
        ExpireTime = expireTime;
    }

    public Boolean getForceOutofDate() {
        return ForceOutofDate;
    }

    public void setForceOutofDate(Boolean forceOutofDate) {
        ForceOutofDate = forceOutofDate;
    }

    public T getValue() {
        return Value;
    }

    public void setValue(T value) {
        Value = value;
    }
}
