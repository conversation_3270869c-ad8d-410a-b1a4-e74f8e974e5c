package com.zysj.system.domain.data;

import lombok.*;

/**
 * <AUTHOR> loso
 * @version : v1.0
 * @date : Created in 2025/1/3 21:30
 * @description : 径向数据
 * @modified By : loso
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class RadialData extends AbstractRadarData {

    //径向风
    private Double[] velocityArray;
    //信噪比
    private Double[] snrArray;
    private Double[] cnrArray;
    //谱宽
    private Double[] bwArray;
    //置信度
    private Integer[] cfarArray;
    //谱强
    private Integer[] specIntensityArray;


    //原始频谱
    public double[] freqArray;
    //取80附件的频谱数据
    public double[] freqNewArray;
    //计算返回的回波数据值（不使用）
    public double[] specArray;
    //计算返回的新回波数据值
    public double[] specNewcArray;


}
