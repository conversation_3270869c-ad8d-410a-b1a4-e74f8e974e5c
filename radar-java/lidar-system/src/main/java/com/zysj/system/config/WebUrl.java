package com.zysj.system.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration


public class WebUrl {

    @Value("${ladar.ip}")
    private String ladarIp;

    @Value("${ladar.Radar.UpdateInfo}")
    private String Radar_UpdateInfo;
    @Value("${ladar.Radar.Push}")
    private String Radar_Push;

    @Value("${ladar.CaptureCard.InitSerialPort}")
    private String CaptureCard_InitSerialPort;

    @Value("${ladar.CaptureCard.SerialPortOpen}")
    private String CaptureCard_SerialPortOpen;

    @Value("${ladar.CaptureCard.SerialPortClose}")
    private String CaptureCard_SerialPortClose;

    @Value("${ladar.Laser.InitSerialPort}")
    private String Laser_InitSerialPort;

    @Value("${ladar.Laser.SerialPortOpen}")
    private String Laser_SerialPortOpen;

    @Value("${ladar.Laser.SerialPortClose}")
    private String Laser_SerialPortClose;
    @Value("${ladar.Laser.OpenLaser}")
    private String Laser_OpenLaser;

    @Value("${ladar.Laser.ReadParam}")
    private String Laser_ReadParam;

    @Value("${ladar.Laser.CloseLaser}")
    private String Laser_CloseLaser;

    @Value("${ladar.Laser.Start}")
    private String Laser_Start;

    @Value("${ladar.Laser.Close}")
    private String Laser_Close;

    @Value("${ladar.Laser.Push}")
    private String Laser_Push;

    @Value("${ladar.Turntable.InitSerialPort}")
    private String Turntable_InitSerialPort;

    @Value("${ladar.Turntable.SerialPortOpen}")
    private String Turntable_SerialPortOpen;

    @Value("${ladar.Turntable.SerialPortClose}")
    private String Turntable_SerialPortClose;

    @Value("${ladar.Turntable.SetParam}")
    private String Turntable_SetParam;


    @Value("${ladar.THSensor.InitSerialPort}")
    private String THSensor_InitSerialPort;

    @Value("${ladar.THSensor.SerialPortOpen}")
    private String THSensor_SerialPortOpen;

    @Value("${ladar.THSensor.SerialPortClose}")
    private String THSensor_SerialPortClose;


    @Value("${ladar.ScanScheme.write}")
    private String ScanScheme_Write;

    @Value("${ladar.ScanScheme.read}")
    private String ScanScheme_Read;

    @Value("${ladar.WindLidar.init}")
    private String WindLidar_Init;

    @Value("${ladar.WindLidar.start}")
    private String WindLidar_Start;

    @Value("${ladar.WindLidar.stop}")
    private String WindLidar_Stop;

    @Value("${ladar.WindLidar.info}")
    private String WindLidar_Info;

    @Value("${ladar.WindLidar.status}")
    private String WindLidar_Status;

    @Value("${ladar.WindLidar.push}")
    private String WindLidar_Push;

    @Value("${ladar.WindowsOS.getNetworkList}")
    private String WindowsOS_GetNetworkList;

    @Value("${ladar.WindowsOS.getSysInfo}")
    private String WindowsOS_GetSysInfo;


    @Value("${ladar.Config.radarConfig}")
    private String Config_RadarConfig;

    @Value("${ladar.Config.scanScheme}")
    private String Config_ScanScheme;

    @Value("${ladar.Config.site}")
    private String Config_Site;

    @Value("${ladar.Config.product}")
    private String Config_Product;

    @Value("${ladar.Config.scanType}")
    private String Config_ScanType;

    @Value("${ladar.Config.moments}")
    private String Config_Moments;

    @Value("${ladar.DataReceiveSet.push}")
    private String DataReceiveSet_Push;

    public String getLadarIp() {
        return ladarIp;
    }

    public void setLadarIp(String ladarIp) {
        this.ladarIp = ladarIp;
    }

    public String getCaptureCard_InitSerialPort() {
        return CaptureCard_InitSerialPort;
    }

    public void setCaptureCard_InitSerialPort(String captureCard_InitSerialPort) {
        CaptureCard_InitSerialPort = captureCard_InitSerialPort;
    }

    public String getCaptureCard_SerialPortOpen() {
        return CaptureCard_SerialPortOpen;
    }

    public void setCaptureCard_SerialPortOpen(String captureCard_SerialPortOpen) {
        CaptureCard_SerialPortOpen = captureCard_SerialPortOpen;
    }

    public String getCaptureCard_SerialPortClose() {
        return CaptureCard_SerialPortClose;
    }

    public void setCaptureCard_SerialPortClose(String captureCard_SerialPortClose) {
        CaptureCard_SerialPortClose = captureCard_SerialPortClose;
    }

    public String getLaser_InitSerialPort() {
        return Laser_InitSerialPort;
    }

    public void setLaser_InitSerialPort(String laser_InitSerialPort) {
        Laser_InitSerialPort = laser_InitSerialPort;
    }

    public String getLaser_SerialPortOpen() {
        return Laser_SerialPortOpen;
    }

    public void setLaser_SerialPortOpen(String laser_SerialPortOpen) {
        Laser_SerialPortOpen = laser_SerialPortOpen;
    }

    public String getLaser_SerialPortClose() {
        return Laser_SerialPortClose;
    }

    public void setLaser_SerialPortClose(String laser_SerialPortClose) {
        Laser_SerialPortClose = laser_SerialPortClose;
    }

    public String getLaser_OpenLaser() {
        return Laser_OpenLaser;
    }

    public void setLaser_OpenLaser(String laser_OpenLaser) {
        Laser_OpenLaser = laser_OpenLaser;
    }

    public String getLaser_ReadParam() {
        return Laser_ReadParam;
    }

    public void setLaser_ReadParam(String laser_ReadParam) {
        Laser_ReadParam = laser_ReadParam;
    }

    public String getLaser_CloseLaser() {
        return Laser_CloseLaser;
    }

    public void setLaser_CloseLaser(String laser_CloseLaser) {
        Laser_CloseLaser = laser_CloseLaser;
    }

    public String getTurntable_InitSerialPort() {
        return Turntable_InitSerialPort;
    }

    public void setTurntable_InitSerialPort(String turntable_InitSerialPort) {
        Turntable_InitSerialPort = turntable_InitSerialPort;
    }

    public String getTurntable_SerialPortOpen() {
        return Turntable_SerialPortOpen;
    }

    public void setTurntable_SerialPortOpen(String turntable_SerialPortOpen) {
        Turntable_SerialPortOpen = turntable_SerialPortOpen;
    }

    public String getTurntable_SerialPortClose() {
        return Turntable_SerialPortClose;
    }

    public void setTurntable_SerialPortClose(String turntable_SerialPortClose) {
        Turntable_SerialPortClose = turntable_SerialPortClose;
    }

    public String getTHSensor_InitSerialPort() {
        return THSensor_InitSerialPort;
    }

    public void setTHSensor_InitSerialPort(String THSensor_InitSerialPort) {
        this.THSensor_InitSerialPort = THSensor_InitSerialPort;
    }

    public String getTHSensor_SerialPortOpen() {
        return THSensor_SerialPortOpen;
    }

    public void setTHSensor_SerialPortOpen(String THSensor_SerialPortOpen) {
        this.THSensor_SerialPortOpen = THSensor_SerialPortOpen;
    }

    public String getTHSensor_SerialPortClose() {
        return THSensor_SerialPortClose;
    }

    public void setTHSensor_SerialPortClose(String THSensor_SerialPortClose) {
        this.THSensor_SerialPortClose = THSensor_SerialPortClose;
    }

    public String getScanScheme_Write() {
        return ScanScheme_Write;
    }

    public void setScanScheme_Write(String scanScheme_Write) {
        ScanScheme_Write = scanScheme_Write;
    }

    public String getScanScheme_Read() {
        return ScanScheme_Read;
    }

    public void setScanScheme_Read(String scanScheme_Read) {
        ScanScheme_Read = scanScheme_Read;
    }

    public String getConfig_ScanScheme() {
        return Config_ScanScheme;
    }

    public void setConfig_ScanScheme(String config_ScanScheme) {
        Config_ScanScheme = config_ScanScheme;
    }

    public String getWindLidar_Init() {
        return WindLidar_Init;
    }

    public void setWindLidar_Init(String windLidar_Init) {
        WindLidar_Init = windLidar_Init;
    }

    public String getWindLidar_Start() {
        return WindLidar_Start;
    }

    public void setWindLidar_Start(String windLidar_Start) {
        WindLidar_Start = windLidar_Start;
    }

    public String getWindLidar_Stop() {
        return WindLidar_Stop;
    }

    public void setWindLidar_Stop(String windLidar_Stop) {
        WindLidar_Stop = windLidar_Stop;
    }

    public String getWindLidar_Info() {
        return WindLidar_Info;
    }

    public void setWindLidar_Info(String windLidar_Info) {
        WindLidar_Info = windLidar_Info;
    }

    public String getWindLidar_Status() {
        return WindLidar_Status;
    }

    public void setWindLidar_Status(String windLidar_Status) {
        WindLidar_Status = windLidar_Status;
    }

    public String getWindowsOS_GetNetworkList() {
        return WindowsOS_GetNetworkList;
    }

    public void setWindowsOS_GetNetworkList(String windowsOS_GetNetworkList) {
        WindowsOS_GetNetworkList = windowsOS_GetNetworkList;
    }

    public String getWindowsOS_GetSysInfo() {
        return WindowsOS_GetSysInfo;
    }

    public void setWindowsOS_GetSysInfo(String windowsOS_GetSysInfo) {
        WindowsOS_GetSysInfo = windowsOS_GetSysInfo;
    }


    public String getRadar_UpdateInfo() {
        return Radar_UpdateInfo;
    }

    public void setRadar_UpdateInfo(String radar_UpdateInfo) {
        Radar_UpdateInfo = radar_UpdateInfo;
    }

    public String getTurntable_SetParam() {
        return Turntable_SetParam;
    }

    public void setTurntable_SetParam(String turntable_SetParam) {
        Turntable_SetParam = turntable_SetParam;
    }

    public String getConfig_RadarConfig() {
        return Config_RadarConfig;
    }

    public void setConfig_RadarConfig(String config_RadarConfig) {
        Config_RadarConfig = config_RadarConfig;
    }

    public String getConfig_Site() {
        return Config_Site;
    }

    public void setConfig_Site(String config_Site) {
        Config_Site = config_Site;
    }

    public String getConfig_Product() {
        return Config_Product;
    }

    public void setConfig_Product(String config_Product) {
        Config_Product = config_Product;
    }

    public String getConfig_ScanType() {
        return Config_ScanType;
    }

    public void setConfig_ScanType(String config_ScanType) {
        Config_ScanType = config_ScanType;
    }

    public String getConfig_Moments() {
        return Config_Moments;
    }

    public void setConfig_Moments(String config_Moments) {
        Config_Moments = config_Moments;
    }

    public String getLaser_Start() {
        return Laser_Start;
    }

    public void setLaser_Start(String laser_Start) {
        Laser_Start = laser_Start;
    }

    public String getLaser_Push() {
        return Laser_Push;
    }

    public void setLaser_Push(String laser_Push) {
        Laser_Push = laser_Push;
    }

    public String getRadar_Push() {
        return Radar_Push;
    }

    public void setRadar_Push(String radar_Push) {
        Radar_Push = radar_Push;
    }

    public String getWindLidar_Push() {
        return WindLidar_Push;
    }

    public void setWindLidar_Push(String windLidar_Push) {
        WindLidar_Push = windLidar_Push;
    }

    public String getLaser_Close() {
        return Laser_Close;
    }

    public void setLaser_Close(String laser_Close) {
        Laser_Close = laser_Close;
    }

    public String getDataReceiveSet_Push() {
        return DataReceiveSet_Push;
    }

    public void setDataReceiveSet_Push(String dataReceiveSet_Push) {
        DataReceiveSet_Push = dataReceiveSet_Push;
    }
}
