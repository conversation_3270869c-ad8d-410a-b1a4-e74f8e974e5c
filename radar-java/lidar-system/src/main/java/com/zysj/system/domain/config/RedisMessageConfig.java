package com.zysj.system.domain.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Qualifier;
import com.zysj.system.listener.RedisMessageSubscriber;

@Configuration
public class RedisMessageConfig {

    private final RedisMessageSubscriber redisMessageSubscriber;

    public RedisMessageConfig(RedisMessageSubscriber redisMessageSubscriber) {
        this.redisMessageSubscriber = redisMessageSubscriber;
    }

    @Bean
    RedisMessageListenerContainer container(@Qualifier("messageRedisConnectionFactory") RedisConnectionFactory connectionFactory,
                                          MessageListenerAdapter listenerAdapter) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.addMessageListener(listenerAdapter, new ChannelTopic("radial_data"));
        container.addMessageListener(listenerAdapter, new ChannelTopic("wind_profile_data"));
        container.addMessageListener(listenerAdapter, new ChannelTopic("scan_task"));
        container.addMessageListener(listenerAdapter, new ChannelTopic("scan_sub_task"));
        container.addMessageListener(listenerAdapter, new ChannelTopic("turntable_status"));

        container.addMessageListener(listenerAdapter, new ChannelTopic("freq_spec_data"));
        container.addMessageListener(listenerAdapter, new ChannelTopic("THSenSor_status"));
        container.addMessageListener(listenerAdapter, new ChannelTopic("laser_status"));
        container.addMessageListener(listenerAdapter, new ChannelTopic("Sys_Sum_Info"));

        container.addMessageListener(listenerAdapter, new ChannelTopic("minio_file_info"));

        return container;
    }

    @Bean
    MessageListenerAdapter messageListener() {
        return new MessageListenerAdapter(redisMessageSubscriber);
    }
} 