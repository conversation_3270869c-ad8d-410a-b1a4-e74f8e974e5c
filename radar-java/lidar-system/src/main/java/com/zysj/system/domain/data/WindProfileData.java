package com.zysj.system.domain.data;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> loso
 * @version : v1.0
 * @date : Created in 2025/1/3 21:58
 * @description : 风廓线数据
 * @modified By : loso
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WindProfileData extends AbstractRadarData {
    // 方位角
    private Float[] azimuthAngleArray;

    // 垂直风速
    private Float[] ivf;

    // 水平风向
    private Float[] ivh;

    // 水平风向
    private Float[] ibeta0;


    //风速的二维数据
    public float[] velocityArray2D;

    public Integer[] velocityPointerArray;


}
