package com.zysj.framework.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

@Component
@ConfigurationProperties(prefix = "spring.redis")
public class RedisProperties {
    
    @NestedConfigurationProperty
    private RedisConfig default_;
    
    @NestedConfigurationProperty
    private RedisConfig message;

    public static class RedisConfig {
        private String host;
        private int port;
        private int database;
        private String password;
        private String timeout;
        private Pool lettuce;

        public static class Pool {
            private int minIdle;
            private int maxIdle;
            private int maxActive;
            private String maxWait;

            public int getMinIdle() {
                return minIdle;
            }

            public void setMinIdle(int minIdle) {
                this.minIdle = minIdle;
            }

            public int getMaxIdle() {
                return maxIdle;
            }

            public void setMaxIdle(int maxIdle) {
                this.maxIdle = maxIdle;
            }

            public int getMaxActive() {
                return maxActive;
            }

            public void setMaxActive(int maxActive) {
                this.maxActive = maxActive;
            }

            public String getMaxWait() {
                return maxWait;
            }

            public void setMaxWait(String maxWait) {
                this.maxWait = maxWait;
            }
        }

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public int getDatabase() {
            return database;
        }

        public void setDatabase(int database) {
            this.database = database;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getTimeout() {
            return timeout;
        }

        public void setTimeout(String timeout) {
            this.timeout = timeout;
        }

        public Pool getLettuce() {
            return lettuce;
        }

        public void setLettuce(Pool lettuce) {
            this.lettuce = lettuce;
        }
    }

    public RedisConfig getDefault() {
        return default_;
    }

    public void setDefault(RedisConfig default_) {
        this.default_ = default_;
    }

    public RedisConfig getMessage() {
        return message;
    }

    public void setMessage(RedisConfig message) {
        this.message = message;
    }
} 