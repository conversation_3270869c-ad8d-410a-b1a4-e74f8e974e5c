package com.zysj.framework.config;

import lombok.Data;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

@Data
public class SiteConfig {
    private byte[] siteCode;
    private byte[] siteName;
    private float latitude;
    private float longitude;
    private float siteHeight;
    private float windowHeight;
    private int wavelength;
    private float softwareVersion;
    private byte radarType;

    public SiteConfig(String siteCode, String siteName, float latitude, float longitude, float siteHeight, float windowHeight, int wavelength, float softwareVersion, byte radarType) {
        this.siteCode = padBytes(siteCode, 16);
        this.siteName = padBytes(siteName, 32);
        this.latitude = latitude;
        this.longitude = longitude;
        this.siteHeight = siteHeight;
        this.windowHeight = windowHeight;
        this.wavelength = wavelength;
        this.softwareVersion = softwareVersion;
        this.radarType = radarType;
    }

    private byte[] padBytes(String str, int length) {
        ByteBuffer buffer = ByteBuffer.allocate(length);
        buffer.put(str.getBytes(StandardCharsets.UTF_8));
        return buffer.array();
    }

    public byte[] getSiteCode() {
        return siteCode;
    }

    public byte[] getSiteName() {
        return siteName;
    }

    public float getLatitude() {
        return latitude;
    }

    public float getLongitude() {
        return longitude;
    }

    public float getSiteHeight() {
        return siteHeight;
    }

    public float getWindowHeight() {
        return windowHeight;
    }

    public int getWavelength() {
        return wavelength;
    }

    public float getSoftwareVersion() {
        return softwareVersion;
    }

    public byte getRadarType() {
        return radarType;
    }
}
