package com.zysj.framework.config;

import lombok.Data;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

@Data
public class TaskConfig {
    private byte[] taskName;
    private byte[] taskDescription;
    private byte scanType;
    private int pulseWidth;
    private long startTimestamp;
    private byte cutNum;
    private float scannerNorthCorrect;
    private double aomFreqShift;

    public TaskConfig(String taskName, String taskDescription, int scanType, int pulseWidth, long startTimestamp, int cutNum, float scannerNorthCorrect, double aomFreqShift) {
        this.taskName = padBytes(taskName, 32);
        this.taskDescription = padBytes(taskDescription, 160);
        this.scanType = (byte) (scanType + 1);
        this.pulseWidth = pulseWidth;
        this.startTimestamp = startTimestamp;
        this.cutNum = (byte) cutNum;
        this.scannerNorthCorrect = scannerNorthCorrect;
        this.aomFreqShift = aomFreqShift;
    }

    private byte[] padBytes(String str, int length) {
        ByteBuffer buffer = ByteBuffer.allocate(length);
        buffer.put(str.getBytes(StandardCharsets.UTF_8));
        return buffer.array();
    }

    public byte[] getTaskName() {
        return taskName;
    }

    public byte[] getTaskDescription() {
        return taskDescription;
    }

    public byte getScanType() {
        return scanType;
    }

    public int getPulseWidth() {
        return pulseWidth;
    }

    public long getStartTimestamp() {
        return startTimestamp;
    }

    public byte getCutNum() {
        return cutNum;
    }

    public float getScannerNorthCorrect() {
        return scannerNorthCorrect;
    }

    public double getAomFreqShift() {
        return aomFreqShift;
    }
}