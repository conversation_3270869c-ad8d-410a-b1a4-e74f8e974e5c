package com.zysj.framework.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import java.time.Duration;

@Configuration
public class MultiRedisConfig {

    @Autowired
    private RedisProperties redisProperties;

    @Bean
    @Primary
    public RedisConnectionFactory defaultRedisConnectionFactory() {
        RedisProperties.RedisConfig config = redisProperties.getDefault();
        if (config == null) {
            throw new IllegalStateException("Default Redis configuration is missing. Please check your application.yml");
        }
        return createConnectionFactory(config);
    }

    @Bean
    public RedisConnectionFactory messageRedisConnectionFactory() {
        RedisProperties.RedisConfig config = redisProperties.getMessage();
        if (config == null) {
            throw new IllegalStateException("Message Redis configuration is missing. Please check your application.yml");
        }
        return createConnectionFactory(config);
    }

    private RedisConnectionFactory createConnectionFactory(RedisProperties.RedisConfig config) {
        RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration();
        redisConfig.setHostName(config.getHost());
        redisConfig.setPort(config.getPort());
        redisConfig.setDatabase(config.getDatabase());
        if (config.getPassword() != null && !config.getPassword().isEmpty()) {
            redisConfig.setPassword(config.getPassword());
        }

        GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
        if (config.getLettuce() != null) {
            poolConfig.setMinIdle(config.getLettuce().getMinIdle());
            poolConfig.setMaxIdle(config.getLettuce().getMaxIdle());
            poolConfig.setMaxTotal(config.getLettuce().getMaxActive());
            poolConfig.setMaxWaitMillis(parseDuration(config.getLettuce().getMaxWait()));
        }

        LettucePoolingClientConfiguration clientConfig = LettucePoolingClientConfiguration.builder()
                .commandTimeout(Duration.parse("PT" + config.getTimeout()))
                .poolConfig(poolConfig)
                .build();

        return new LettuceConnectionFactory(redisConfig, clientConfig);
    }

    private long parseDuration(String duration) {
        if (duration == null || duration.isEmpty()) {
            return -1;
        }
        if (duration.endsWith("ms")) {
            return Long.parseLong(duration.substring(0, duration.length() - 2));
        }
        return -1;
    }

    @Bean
    @Primary
    public RedisTemplate<Object, Object> redisTemplate(RedisConnectionFactory defaultRedisConnectionFactory) {
        return createRedisTemplate(defaultRedisConnectionFactory);
    }

    @Bean
    public RedisTemplate<Object, Object> messageRedisTemplate(RedisConnectionFactory messageRedisConnectionFactory) {
        return createRedisTemplate(messageRedisConnectionFactory);
    }

    private RedisTemplate<Object, Object> createRedisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<Object, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        FastJson2JsonRedisSerializer<Object> serializer = new FastJson2JsonRedisSerializer<>(Object.class);

        // 使用StringRedisSerializer来序列化和反序列化redis的key值
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(serializer);

        // Hash的key也采用StringRedisSerializer的序列化方式
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(serializer);

        template.afterPropertiesSet();
        return template;
    }
} 