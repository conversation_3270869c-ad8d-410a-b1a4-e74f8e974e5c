# 项目相关配置
zysj:
  # 名称
  name: zysj-ladar
  # 版本
  version: 1.0.1
  # 版权年份
  copyrightYear: 2024
  # 文件路径 示例（ Windows配置D:/zysj/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /Users/<USER>/IdeaProjects/radar/upload
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 9000
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.zysj: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 默认数据源配置
    default:
      # 地址 主地址
      host: 127.0.0.1
      # 端口，默认为6379
      port: 6379
      # 数据库索引
      database: 3
      # 密码
      password:
      # 连接超时时间
      timeout: 10s
      lettuce:
        pool:
          # 连接池中的最小空闲连接
          min-idle: 0
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池的最大数据库连接数
          max-active: 8
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
    # 消息队列数据源配置
    message:
#      host: ************
      host: 127.0.0.1
      port: 6379
      database: 0
      password:
      timeout: 20s
      lettuce:
        pool:
          min-idle: 0
          max-idle: 8
          max-active: 8
          max-wait: -1ms
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        url: **********************************************************************************************************************************************
        username: root
        password: M3u0VLBBFsW1vOQP
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: zysj
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.zysj.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: false
    global-config:
      db-config:
        logic-delete-field: isDelete # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
        logic-delete-value: 1 # 逻辑已删除值(默认为 1)
        logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping:

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

okhttp:
  connect-timeout: 5000 #socket连接超时时间（秒）
  read-timeout: 8000
  write-timeout: 8000
  max-idle-connections: 10 #最大空闲连接数
  keep-alive-duration: 300 #空闲连接最多存活时间（秒）


myapp:
  radarUrl: http://*************:5133


#minio配置
minio:
  access-key: admin
  secret-key: admin123456
  url: http://*************:9000
  bucket-name: demo

ladar:
  ip: http://************:5133
  CaptureCard:
    InitSerialPort: /api/device/CaptureCard/InitSerialPort
    SerialPortOpen: /api/device/CaptureCard/SerialPortOpen
    SerialPortClose: /api/device/CaptureCard/SerialPortClose

  Laser:
    InitSerialPort: /api/device/Laser/InitSerialPort
    SerialPortOpen: /api/device/Laser/SerialPortOpen
    SerialPortClose: /api/device/Laser/SerialPortClose
    OpenLaser: /api/device/Laser/OpenLaser
    ReadParam: /api/device/Laser/ReadParam
    CloseLaser: /api/device/Laser/CloseLaser
    Start: /api/device/Laser/SetParam
    Close: /api/device/Laser/closeLaser
    Push: /api/device/LaserMark/SetLaserMark

  Radar:
    UpdateInfo: /api/device/Radar/UpdateInfo
    Push: /api/device/LadarMark/SetLadarMark

  Turntable:
    InitSerialPort: /api/device/Turntable/InitSerialPort
    SerialPortOpen: /api/device/Turntable/SerialPortOpen
    SerialPortClose: /api/device/Turntable/SerialPortClose
    SetParam: /api/device/Turntable/SetParam
  THSensor:
    InitSerialPort: /api/device/THSensor/InitSerialPort
    SerialPortOpen: /api/device/THSensor/SerialPortOpen
    SerialPortClose: /api/device/THSensor/SerialPortClose
  ScanScheme:
    write: /api/SysConfig/ScanScheme/write
    read: /api/SysConfig/ScanScheme/read

  WindLidar:
    init: /api/WindLidar/init
    start: /api/WindLidar/start
    stop: /api/WindLidar/stop
    info: /api/WindLidar/info
    status: /api/WindLidar/status
    push: /api/device/WindMark/SetWindMark

  WindowsOS:
    getNetworkList: /api/system/WindowsOS/getNetworkList
    getSysInfo: /api/system/WindowsOS/getSysInfo

  Config:
    scanScheme: /api/SysConfig/ScanScheme/SetScanSchemes
    radarConfig: /api/SysConfig/Ladar/SetLadarConfig
    site: /api/SysConfig/Site/SetSiteInfo
    product: /api/SysConfig/ProductType/SetProducts
    scanType: /api/SysConfig/ScanType/SetScanTypes
    moments: /api/SysConfig/MomentsType/SetMomentss

  DataReceiveSet:
    push: /api/device/FileConfig/SetFileConfig