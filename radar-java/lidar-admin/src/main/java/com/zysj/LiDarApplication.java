package com.zysj;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableScheduling
public class LiDarApplication {
    public static void main(String[] args) {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication.run(LiDarApplication.class, args);
        System.out.println(
                "(♥◠‿◠)ﾉﾞ  激光雷达启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                "     |    |  |    |  \n" +
                "     |__\\ /__|__\\ /__  \n" +
                "    /____|____\\____\\  \n" +
                "   |  __|  __|  __|  |  \n" +
                "   | |  | |  | |  | | |  \n" +
                "   | |__| |__| |__| | |  \n" +
                "   |    |    |    |  |  \n" +
                "   \\____/\\____/\\____/  \n" +
                "     \\    /    \\    /  \n" +
                "      \\__/      \\__/  \n" +
                "       ^         ^  \n" +
                "       |         |  \n" +
                "      (LiDAR)");
    }
}
