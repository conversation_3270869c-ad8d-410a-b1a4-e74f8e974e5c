package com.zysj.web.controller.device;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.device.Compass;
import com.zysj.system.service.device.ICompassService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 指北针修正管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-26
 */
@RestController
@RequestMapping("/device/compass")
public class CompassController extends BaseController
{
    @Autowired
    private ICompassService compassService;

    /**
     * 查询指北针修正管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:compass:list')")
    @GetMapping("/list")
    public TableDataInfo list(Compass compass)
    {
        startPage();
        List<Compass> list = compassService.getList(compass);
        return getDataTable(list);
    }

    /**
     * 查询指北针修正管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:compass:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(Compass compass)
    {

        List<Compass> list = compassService.getList(compass);
        return getDataTable(list);
    }


    /**
     * 导出指北针修正管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:compass:export')")
    @Log(title = "指北针修正管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Compass compass)
    {
        List<Compass> list = compassService.getList(compass);
        ExcelUtil<Compass> util = new ExcelUtil<Compass>(Compass.class);
        util.exportExcel(response, list, "指北针修正管理数据");
    }

    /**
     * 获取指北针修正管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:compass:query')")
    @GetMapping(value = "/{compassId}")
    public AjaxResult getInfo(@PathVariable("compassId") Long compassId)
    {
        return success(compassService.getInfo(compassId));
    }

    /**
     * 新增指北针修正管理
     */
    @PreAuthorize("@ss.hasPermi('system:compass:add')")
    @Log(title = "指北针修正管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Compass compass)
    {
        return toAjax(compassService.insert(compass));
    }

    /**
     * 修改指北针修正管理
     */
    @PreAuthorize("@ss.hasPermi('system:compass:edit')")
    @Log(title = "指北针修正管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Compass compass)
    {
        return toAjax(compassService.update(compass));
    }

    /**
     * 删除指北针修正管理
     */
    @PreAuthorize("@ss.hasPermi('system:compass:remove')")
    @Log(title = "指北针修正管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{compassIds}")
    public AjaxResult remove(@PathVariable Long[] compassIds)
    {
        return toAjax(compassService.deleteByIds(compassIds));
    }
}
