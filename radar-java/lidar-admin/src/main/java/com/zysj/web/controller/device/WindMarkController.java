package com.zysj.web.controller.device;

import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.core.page.TableDataInfo;
import com.zysj.common.enums.BusinessType;
import com.zysj.common.utils.DateUtils;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.system.domain.device.WindMark;
import com.zysj.system.service.device.IWindMarkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 风速标定Controller
 *
 * <AUTHOR>
 * @date 2025-02-12
 */
@Api(tags = "风速标定")
@RestController
@RequestMapping("/device/windMark")
public class WindMarkController extends BaseController {
    @Autowired
    private IWindMarkService windMarkService;

    /**
     * 查询风速标定列表
     */
    @ApiOperation(value = "查询风速标定列表")
    @PreAuthorize("@ss.hasPermi('system:windMark:list')")
    @GetMapping("/list")
    public TableDataInfo list(WindMark windMark) {
        startPage();
        List<WindMark> list = windMarkService.getList(windMark);
        return getDataTable(list);
    }

    /**
     * 查询风速标定列表
     */
    @ApiOperation(value = "查询风速标定列表")
    @PreAuthorize("@ss.hasPermi('system:windMark:list')")
    @GetMapping("/listAll")
    public AjaxResult getAllList(WindMark windMark) {
        List<WindMark> list = windMarkService.getList(windMark);
        return success(list);
    }


    /**
     * 导出风速标定列表
     */
    @ApiOperation(value = "导出风速标定列表")
    @PreAuthorize("@ss.hasPermi('system:windMark:export')")
    @Log(title = "风速标定", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WindMark windMark) {
        List<WindMark> list = windMarkService.getList(windMark);
        ExcelUtil<WindMark> util = new ExcelUtil<WindMark>(WindMark.class);
        util.exportExcel(response, list, "风速标定数据");
    }

    /**
     * 获取风速标定详细信息
     */
    @ApiOperation(value = "获取风速标定详细信息")
    @PreAuthorize("@ss.hasPermi('system:windMark:query')")
    @GetMapping(value = "/{windMarkId}")
    public AjaxResult getInfo(@PathVariable("windMarkId") Long windMarkId) {
        return success(windMarkService.getInfo(windMarkId));
    }

    /**
     * 新增风速标定
     */
    @ApiOperation(value = "新增风速标定")
    @PreAuthorize("@ss.hasPermi('system:windMark:add')")
    @Log(title = "风速标定", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WindMark windMark) {
        windMark.setCreateBy(getUsername());
        windMark.setUserId(String.valueOf(getUserId()));
        windMark.setMarkTime(DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        return toAjax(windMarkService.insert(windMark));
    }

    /**
     * 修改风速标定
     */
    @ApiOperation(value = "修改风速标定")
    @PreAuthorize("@ss.hasPermi('system:windMark:edit')")
    @Log(title = "风速标定", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WindMark windMark) {
        windMark.setUpdateBy(getUsername());
        return toAjax(windMarkService.update(windMark));
    }

    /**
     * 删除风速标定
     */
    @ApiOperation(value = "删除风速标定")
    @PreAuthorize("@ss.hasPermi('system:windMark:remove')")
    @Log(title = "风速标定", businessType = BusinessType.DELETE)
    @DeleteMapping("/{windMarkIds}")
    public AjaxResult remove(@PathVariable Long[] windMarkIds) {
        return toAjax(windMarkService.deleteByIds(windMarkIds));
    }

    /**
     * 雷达标定推送
     */
    @ApiOperation(value="风速标定推送")
    @PreAuthorize("@ss.hasPermi('system:windMark:push')")
    @Log(title = "风速标定推送", businessType = BusinessType.PUSH)
    @PostMapping("/push")
    public AjaxResult push(@RequestBody WindMark windMark) throws Exception {
        return toAjax(windMarkService.push(windMark));
    }
}
