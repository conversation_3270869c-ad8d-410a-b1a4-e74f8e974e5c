package com.zysj.web.controller.status;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.status.SystemAlarmCategory;
import com.zysj.system.service.status.ISystemAlarmCategoryService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2025-02-03
 */
@RestController
@RequestMapping("/system/systemAlarmCategory")
public class SystemAlarmCategoryController extends BaseController
{
    @Autowired
    private ISystemAlarmCategoryService systemAlarmCategoryService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmCategory:list')")
    @GetMapping("/list")
    public TableDataInfo list(SystemAlarmCategory systemAlarmCategory)
    {
        startPage();
        List<SystemAlarmCategory> list = systemAlarmCategoryService.getList(systemAlarmCategory);
        return getDataTable(list);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmCategory:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(SystemAlarmCategory systemAlarmCategory)
    {

        List<SystemAlarmCategory> list = systemAlarmCategoryService.getList(systemAlarmCategory);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmCategory:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SystemAlarmCategory systemAlarmCategory)
    {
        List<SystemAlarmCategory> list = systemAlarmCategoryService.getList(systemAlarmCategory);
        ExcelUtil<SystemAlarmCategory> util = new ExcelUtil<SystemAlarmCategory>(SystemAlarmCategory.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmCategory:query')")
    @GetMapping(value = "/{systemAlarmCategoryId}")
    public AjaxResult getInfo(@PathVariable("systemAlarmCategoryId") Long systemAlarmCategoryId)
    {
        return success(systemAlarmCategoryService.getInfo(systemAlarmCategoryId));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmCategory:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SystemAlarmCategory systemAlarmCategory)
    {
        return toAjax(systemAlarmCategoryService.insert(systemAlarmCategory));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmCategory:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SystemAlarmCategory systemAlarmCategory)
    {
        return toAjax(systemAlarmCategoryService.update(systemAlarmCategory));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmCategory:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{systemAlarmCategoryIds}")
    public AjaxResult remove(@PathVariable Long[] systemAlarmCategoryIds)
    {
        return toAjax(systemAlarmCategoryService.deleteByIds(systemAlarmCategoryIds));
    }
}
