package com.zysj.web.controller.device;

import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.core.page.TableDataInfo;
import com.zysj.common.enums.BusinessType;
import com.zysj.common.utils.DateUtils;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.system.domain.device.LaserMark;
import com.zysj.system.service.device.ILaserMarkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.aspectj.weaver.loadtime.Aj;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 激光器标定Controller
 *
 * <AUTHOR>
 * @date 2025-02-12
 */
@Api(tags = "激光器标定")
@RestController
@RequestMapping("/device/laserMark")
public class LaserMarkController extends BaseController {
    @Autowired
    private ILaserMarkService laserMarkService;

    /**
     * 查询激光器标定列表
     */
    @ApiOperation(value = "查询激光器标定列表")
    @PreAuthorize("@ss.hasPermi('system:laserMark:list')")
    @GetMapping("/list")
    public TableDataInfo list(LaserMark laserMark) {
        startPage();
        List<LaserMark> list = laserMarkService.getList(laserMark);
        return getDataTable(list);
    }

    /**
     * 查询激光器标定列表
     */
    @ApiOperation(value = "查询激光器标定列表")
    @PreAuthorize("@ss.hasPermi('system:laserMark:list')")
    @GetMapping("/listAll")
    public AjaxResult getAllList(LaserMark laserMark) {
        List<LaserMark> list = laserMarkService.getList(laserMark);
        return success(list);
    }


    /**
     * 导出激光器标定列表
     */
    @ApiOperation(value = "导出激光器标定列表")
    @PreAuthorize("@ss.hasPermi('system:laserMark:export')")
    @Log(title = "激光器标定", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LaserMark laserMark) {
        List<LaserMark> list = laserMarkService.getList(laserMark);
        ExcelUtil<LaserMark> util = new ExcelUtil<LaserMark>(LaserMark.class);
        util.exportExcel(response, list, "激光器标定数据");
    }

    /**
     * 获取激光器标定详细信息
     */
    @ApiOperation(value = "获取激光器标定详细信息")
    @PreAuthorize("@ss.hasPermi('system:laserMark:query')")
    @GetMapping(value = "/{radarMarkId}")
    public AjaxResult getInfo(@PathVariable("radarMarkId") Long radarMarkId) {
        return success(laserMarkService.getInfo(radarMarkId));
    }

    /**
     * 新增激光器标定
     */
    @ApiOperation(value = "新增激光器标定")
    @PreAuthorize("@ss.hasPermi('system:laserMark:add')")
    @Log(title = "激光器标定", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LaserMark laserMark) {
        laserMark.setCreateBy(getUsername());
        laserMark.setUserId(String.valueOf(getUserId()));
        laserMark.setMarkTime(DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        return toAjax(laserMarkService.insert(laserMark));
    }

    /**
     * 修改激光器标定
     */
    @ApiOperation(value = "修改激光器标定")
    @PreAuthorize("@ss.hasPermi('system:laserMark:edit')")
    @Log(title = "激光器标定", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LaserMark laserMark) {

        return toAjax(laserMarkService.update(laserMark));
    }

    /**
     * 删除激光器标定
     */
    @ApiOperation(value = "删除激光器标定")
    @PreAuthorize("@ss.hasPermi('system:laserMark:remove')")
    @Log(title = "激光器标定", businessType = BusinessType.DELETE)
    @DeleteMapping("/{radarMarkIds}")
    public AjaxResult remove(@PathVariable Long[] radarMarkIds) {
        return toAjax(laserMarkService.deleteByIds(radarMarkIds));
    }

    /**
     * 激光器标定推送
     */
    @ApiOperation(value = "激光器标定推送")
    @PreAuthorize("@ss.hasPermi('system:laserMark:push')")
    @Log(title = "激光器标定推送", businessType = BusinessType.PUSH)
    @PostMapping("/push")
    public AjaxResult push(@RequestBody LaserMark laserMark) throws Exception {
        return toAjax(laserMarkService.push(laserMark));
    }
}
