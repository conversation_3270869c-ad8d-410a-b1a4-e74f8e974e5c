package com.zysj.web.controller.data;

import com.zysj.common.utils.DateUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.Reader;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class WindStatistics {

    // TRUE 2024-12-29-12点后数据      FALSE 2024-12-29-12点前数据
    public static final Boolean IS29 = false;

    public static void main(String[] args) {
        String csvFilePath = "I:\\工作内容\\2024-12\\气象\\2024-12-30\\2024-12-24至2025-01-09(调整0°)(风塔开始时间对比).csv"; // 2024-12-24至2025-01-09(风塔开始时间对比).csv
        String dataFile = "";
        if (IS29) {
            dataFile = "I:\\工作内容\\2024-12\\气象\\2024-12-30\\2024-12-29-12点后数据.xlsx"; // 替换为你的CSV文件路径
        } else {
            dataFile = "I:\\工作内容\\2024-12\\气象\\2024-12-30\\2024-12-29-12点前数据.xlsx"; // 替换为你的CSV文件路径
        }

        List<Integer> hList = new ArrayList<>();
        Map<String, List<Double>> windSpeeds = new HashMap<>();
        Map<String, List<Double>> windDirections = new HashMap<>();
        // 初始化高度对应的列表
        for (int i = 2; i < 14; i++) { // 1040/20 = 52，从40米到1040米，每20米一个数据点 目前只循环到 240米
            int height = i * 20;
            hList.add(height);
            windSpeeds.put(height + "米雷达风速", new ArrayList<>());
            windSpeeds.put(height + "米风塔风速", new ArrayList<>());
            windDirections.put(height + "米雷达风向", new ArrayList<>());
            windDirections.put(height + "米风塔风向", new ArrayList<>());
        }
        try (Reader reader = new FileReader(csvFilePath);
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.withFirstRecordAsHeader())) {
            for (CSVRecord csvRecord : csvParser) {
                String dateTime = csvRecord.get("DateTime");
                String strDate = "2024-12-29 12:00:00";
                Date date1 = DateUtils.parseDate(dateTime);
                Date date2 = DateUtils.parseDate(strDate);
                if (IS29){
                    if (!date1.before(date2)) {
                        Map<String, Integer> headerMap = csvRecord.getParser().getHeaderMap();
                        // 读取并分组雷达风速数据
                        for (Map.Entry<String, List<Double>> entry : windSpeeds.entrySet()) {
                            String key = entry.getKey();
                            if (headerMap.containsKey(key)) {
                                double value = Double.parseDouble(csvRecord.get(key));
                                entry.getValue().add(value);
                            }
                        }

                        // 读取并分组雷达风向数据
                        for (Map.Entry<String, List<Double>> entry : windDirections.entrySet()) {
                            String key = entry.getKey();
                            if (headerMap.containsKey(key)) {
                                double value = Double.parseDouble(csvRecord.get(key));
                                entry.getValue().add(value);
                            }
                        }
                    }
                }else {
                    if (date1.before(date2)) {
                        Map<String, Integer> headerMap = csvRecord.getParser().getHeaderMap();
                        // 读取并分组雷达风速数据
                        for (Map.Entry<String, List<Double>> entry : windSpeeds.entrySet()) {
                            String key = entry.getKey();

                                try {
                                    if (headerMap.containsKey(key)) {
                                        double value = Double.parseDouble(csvRecord.get(key));
                                        entry.getValue().add(value);
                                    }
                                }catch (Exception e){
                                }

                        }
                        // 读取并分组雷达风向数据
                        for (Map.Entry<String, List<Double>> entry : windDirections.entrySet()) {
                            String key = entry.getKey();
                            try {
                                if (headerMap.containsKey(key)) {
                                    double value = Double.parseDouble(csvRecord.get(key));
                                    entry.getValue().add(value);
                                }
                            }catch (Exception e){
                            }
                        }
                    }
                }
            }

            List<String> valueList = new ArrayList<>();
            // 字符串中提取高度信息
            Function<String, String> getHeight = s -> s.substring(0, s.indexOf("米"));
            //风速 --------------------------------------------------------------------------------------------------------
            Set<String> windSpeedKeys = windSpeeds.keySet();
            List<String> sortedWindSpeedKeys = windSpeedKeys.stream()
                    .sorted() // 排序
                    .collect(Collectors.toList());
            sortedWindSpeedKeys = sort(sortedWindSpeedKeys);
            // 使用流和收集器按照高度进行分组
            TreeMap<String, List<String>> groupedByHeight = new TreeMap<>(sortedWindSpeedKeys.stream()
                    .collect(Collectors.groupingBy(getHeight)));

            //循环设置高度在同一组的
            for (String key : groupedByHeight.keySet()) {
                List<String> heightStr = groupedByHeight.get(key);
                //雷达数据
                List<Double> x = windSpeeds.get(heightStr.get(0));
                //风塔数据
                List<Double> y = windSpeeds.get(heightStr.get(1));
                double variance = calculateRMSD(x, y);
                valueList.add(heightStr.get(0) + "标准差为:" + variance);
                SimpleLinearRegression regression = new SimpleLinearRegression(x, y);
                valueList.add(heightStr.get(0) + "斜率A为:" + regression.getSlope());
                valueList.add(heightStr.get(0) + "截距B为:" + regression.getIntercept());
            }


            //风向 --------------------------------------------------------------------------------------------------------
            Set<String> windDirectionsKeys = windDirections.keySet();
            List<String> sortedWindDirectionsKeys = windDirectionsKeys.stream()
                    .sorted() // 按键的自然顺序排序
                    .collect(Collectors.toList());
            TreeMap<String, List<String>> groupedByWindDirectionsHeight = new TreeMap<>(sortedWindDirectionsKeys.stream()
                    .collect(Collectors.groupingBy(getHeight)));
            for (String key : groupedByWindDirectionsHeight.keySet()) {
                List<String> heightStr = groupedByWindDirectionsHeight.get(key);
                List<Double> x = windDirections.get(heightStr.get(0));
                List<Double> y = windDirections.get(heightStr.get(1));
                double variance = calculateRMSDAdd360(x, y);
                valueList.add(heightStr.get(0) + "标准差为:" + variance);
                SimpleLinearRegression regression = new SimpleLinearRegression(x, y);
                valueList.add(heightStr.get(0) + "斜率A为:" + regression.getSlope());
                valueList.add(heightStr.get(0) + "截距B为:" + regression.getIntercept());
            }

            // 40 60 80 100 120 140 160 180 200 220 分组对比

            for (int i = 0; i < hList.size(); i++) {
                Integer h = hList.get(i);
                Integer hd = hList.get(0);
                Integer ha = hList.get(hList.size() - 1);
                if (h - 20 >= hd) {
                    //风速  取雷达60米对比风塔40米 80米对比60米 100米对比80米 ......
                    List<String> heightStrSpeed = groupedByHeight.get(String.valueOf(h));
                    List<String> heightStrSpeedD = groupedByHeight.get(String.valueOf(h - 20));
                    List<Double> x = windSpeeds.get(heightStrSpeed.get(0));
                    List<Double> y = windSpeeds.get(heightStrSpeedD.get(1));
                    double variance = calculateRMSD(x, y);
                    valueList.add(heightStrSpeed.get(0) + "对比" + heightStrSpeedD.get(1) + "标准差为:" + variance);
                    SimpleLinearRegression regression = new SimpleLinearRegression(x, y);
                    valueList.add(heightStrSpeed.get(0) + "对比" + heightStrSpeedD.get(1) + "斜率A为:" + regression.getSlope());
                    valueList.add(heightStrSpeed.get(0) + "对比" + heightStrSpeedD.get(1) + "截距B为:" + regression.getIntercept());
                    //风向
                    List<String> heightStrDirections = groupedByWindDirectionsHeight.get(String.valueOf(h));
                    List<String> heightStrDirectionsD = groupedByWindDirectionsHeight.get(String.valueOf(h - 20));
                    List<Double> xd = windDirections.get(heightStrDirections.get(0));
                    List<Double> yd = windDirections.get(heightStrDirectionsD.get(1));
                    double varianced = calculateRMSDAdd360(xd, yd);
                    valueList.add(heightStrDirections.get(0) + "对比" + heightStrDirectionsD.get(1) + "标准差为:" + varianced);
                    SimpleLinearRegression regressiona = new SimpleLinearRegression(xd, yd);
                    valueList.add(heightStrDirections.get(0) + "对比" + heightStrDirectionsD.get(1) + "斜率A为:" + regressiona.getSlope());
                    valueList.add(heightStrDirections.get(0) + "对比" + heightStrDirectionsD.get(1) + "截距B为:" + regressiona.getIntercept());
                }
                //  取雷达60米对比风塔80米 60米对比80米 80米对比100米 ......
                if (h + 20 <= ha) {
                    //风速  取雷达60米对比风塔40米 80米对比60米 100米对比80米 ......
                    List<String> heightStrSpeed = groupedByHeight.get(String.valueOf(h));
                    List<String> heightStrSpeedD = groupedByHeight.get(String.valueOf(h + 20));
                    List<Double> x = windSpeeds.get(heightStrSpeed.get(0));
                    List<Double> y = windSpeeds.get(heightStrSpeedD.get(1));
                    double variance = calculateRMSD(x, y);
                    valueList.add(heightStrSpeed.get(0) + "对比" + heightStrSpeedD.get(1) + "标准差为:" + variance);
                    SimpleLinearRegression regression = new SimpleLinearRegression(x, y);
                    valueList.add(heightStrSpeed.get(0) + "对比" + heightStrSpeedD.get(1) + "斜率A为:" + regression.getSlope());
                    valueList.add(heightStrSpeed.get(0) + "对比" + heightStrSpeedD.get(1) + "截距B为:" + regression.getIntercept());
                    //风向
                    List<String> heightStrDirections = groupedByWindDirectionsHeight.get(String.valueOf(h));
                    List<String> heightStrDirectionsD = groupedByWindDirectionsHeight.get(String.valueOf(h + 20));
                    List<Double> xd = windDirections.get(heightStrDirections.get(0));
                    List<Double> yd = windDirections.get(heightStrDirectionsD.get(1));
                    double varianced = calculateRMSDAdd360(xd, yd);
                    valueList.add(heightStrDirections.get(0) + "对比" + heightStrDirectionsD.get(1) + "标准差为:" + varianced);
                    SimpleLinearRegression regressiona = new SimpleLinearRegression(xd, yd);
                    valueList.add(heightStrDirections.get(0) + "对比" + heightStrDirectionsD.get(1) + "斜率A为:" + regressiona.getSlope());
                    valueList.add(heightStrDirections.get(0) + "对比" + heightStrDirectionsD.get(1) + "截距B为:" + regressiona.getIntercept());
                }
            }


            Map<Integer, Map<String, Double>> dataMap = parseData(valueList);

            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("数据");
            // 创建表头
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("高度(米)");
            headerRow.createCell(1).setCellValue("风速标准差");
            headerRow.createCell(2).setCellValue("风速斜率A");
            headerRow.createCell(3).setCellValue("风速截距B");
            headerRow.createCell(4).setCellValue("风向标准差");
            headerRow.createCell(5).setCellValue("风向斜率A");
            headerRow.createCell(6).setCellValue("风向截距B");
            sheet.setDefaultColumnWidth(20);
            // 填充数据
            int rowIndex = 1;
            for (Map.Entry<Integer, Map<String, Double>> entry : dataMap.entrySet()) {
                int height = entry.getKey();
                Map<String, Double> metrics = entry.getValue();
                Row dataRow = sheet.createRow(rowIndex++);
                dataRow.createCell(0).setCellValue(height);
                dataRow.createCell(1).setCellValue(metrics.getOrDefault("风速标准差", 0.0));
                dataRow.createCell(2).setCellValue(metrics.getOrDefault("风速斜率A", 0.0));
                dataRow.createCell(3).setCellValue(metrics.getOrDefault("风速截距B", 0.0));
                dataRow.createCell(4).setCellValue(metrics.getOrDefault("风向标准差", 0.0));
                dataRow.createCell(5).setCellValue(metrics.getOrDefault("风向斜率A", 0.0));
                dataRow.createCell(6).setCellValue(metrics.getOrDefault("风向截距B", 0.0));
            }
            //对比数据 -----------------------------------------------------------------------------------------------------------------
            Map<String, Map<String, Double>> dataMapA = parseDataA(valueList);
            Sheet sheetA = workbook.createSheet("对比数据");
            // 创建表头
            Row headerRowA = sheetA.createRow(0);
            headerRowA.createCell(0).setCellValue("雷达高度对比风塔高度");
            headerRowA.createCell(1).setCellValue("风速标准差");
            headerRowA.createCell(2).setCellValue("风速斜率A");
            headerRowA.createCell(3).setCellValue("风速截距B");
            headerRowA.createCell(4).setCellValue("风向标准差");
            headerRowA.createCell(5).setCellValue("风向斜率A");
            headerRowA.createCell(6).setCellValue("风向截距B");
            sheetA.setDefaultColumnWidth(20);
            // 填充数据
            int rowIndexA = 1;
            for (int i = 0; i < hList.size(); i++) {
                Map<String, Double> metrics = dataMapA.get(String.valueOf(hList.get(i)));
                Map<String, Map<String, Double>> splitData = splitByHeight(metrics);
                for (String key : splitData.keySet()) {
                    Row dataRow = sheetA.createRow(rowIndexA++);
                    System.out.println(key);
                    dataRow.createCell(0).setCellValue(key);
                    dataRow.createCell(1).setCellValue(metrics.getOrDefault(key + "风速标准差", 0.0));
                    dataRow.createCell(2).setCellValue(metrics.getOrDefault(key + "风速斜率A", 0.0));
                    dataRow.createCell(3).setCellValue(metrics.getOrDefault(key + "风速截距B", 0.0));
                    dataRow.createCell(4).setCellValue(metrics.getOrDefault(key + "风向标准差", 0.0));
                    dataRow.createCell(5).setCellValue(metrics.getOrDefault(key + "风向斜率A", 0.0));
                    dataRow.createCell(6).setCellValue(metrics.getOrDefault(key + "风向截距B", 0.0));
                }

            }
            // 导出Excel文件
            try (FileOutputStream fileOut = new FileOutputStream(dataFile)) {
                workbook.write(fileOut);
            } catch (IOException e) {
                e.printStackTrace();
            }
            // 关闭工作簿
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static Map<Integer, Map<String, Double>> parseData(List<String> valueList) {
        Map<Integer, Map<String, Double>> dataMap = new TreeMap<>();
        Pattern pattern = Pattern.compile("(\\d+)米(雷达|风塔)(风速|风向)(标准差|斜率A|截距B)为:(\\d+\\.\\d+)");

        for (String value : valueList) {
            Matcher matcher = pattern.matcher(value);
            if (matcher.matches()) {
                int height = Integer.parseInt(matcher.group(1));
                String metric = matcher.group(3);
                String type = matcher.group(4);
                double valueDouble = Double.parseDouble(matcher.group(5));
                dataMap.putIfAbsent(height, new HashMap<>());
                Map<String, Double> metrics = dataMap.get(height);
                String key = metric + type;
                metrics.put(key, valueDouble);
            }

        }

        // 转换为更易于访问的格式
        Map<Integer, Map<String, Double>> transformedMap = new TreeMap<>();
        for (Map.Entry<Integer, Map<String, Double>> entry : dataMap.entrySet()) {
            int height = entry.getKey();
            Map<String, Double> metrics = entry.getValue();
            Map<String, Double> simplifiedMetrics = new HashMap<>();
            simplifiedMetrics.put("风速标准差", metrics.getOrDefault("风速标准差", 0.0));
            simplifiedMetrics.put("风速斜率A", metrics.getOrDefault("风速斜率A", 0.0));
            simplifiedMetrics.put("风速截距B", metrics.getOrDefault("风速截距B", 0.0));
            simplifiedMetrics.put("风向标准差", metrics.getOrDefault("风向标准差", 0.0));
            simplifiedMetrics.put("风向斜率A", metrics.getOrDefault("风向斜率A", 0.0));
            simplifiedMetrics.put("风向截距B", metrics.getOrDefault("风向截距B", 0.0));
            transformedMap.put(height, simplifiedMetrics);
        }

        return transformedMap;
    }

    private static Map<String, Map<String, Double>> parseDataA(List<String> valueList) {
        Map<String, Map<String, Double>> dataMap = new TreeMap<>();
        Pattern patterna = Pattern.compile("(\\d+)米雷达(风速|风向)对比(\\d+)米风塔(风速|风向)(标准差|斜率A|截距B)为:(\\d+\\.\\d+)");

        for (String value : valueList) {
            Matcher matchera = patterna.matcher(value);
            if (matchera.matches()) {
                String height = matchera.group(1); //雷达高度
                int heighta = Integer.parseInt(matchera.group(3)); //风塔高度
                String metric = matchera.group(2);
                String type = matchera.group(5);
                double valueDouble = Double.parseDouble(matchera.group(6));
                dataMap.putIfAbsent(height, new HashMap<>());
                Map<String, Double> metrics = dataMap.get(height);
                String key = height + "雷达/" + heighta + "风塔" + metric + type;
                metrics.put(key, valueDouble);
            }
        }

        // 转换为更易于访问的格式
        Map<String, Map<String, Double>> transformedMap = new TreeMap<>();
        for (Map.Entry<String, Map<String, Double>> entry : dataMap.entrySet()) {
            String height = entry.getKey();
            Map<String, Double> metrics = entry.getValue();
            Map<String, Double> simplifiedMetrics = new HashMap<>();
            Map<String, Double> sortedMap = new LinkedHashMap<>();
            for (String key : metrics.keySet()) {
                simplifiedMetrics.put(key, metrics.getOrDefault(key, 0.0));
            }
            // 将Map转换为List<Map.Entry>以便排序
            List<Map.Entry<String, Double>> entryList = new ArrayList<>(simplifiedMetrics.entrySet());
            // 使用自定义比较器排序
            entryList.sort(WindStatistics::compareKeysByNumbers);
            // 将排序后的结果放入一个新的LinkedHashMap以保持顺序
            for (Map.Entry<String, Double> entryA : entryList) {
                sortedMap.put(entryA.getKey(), entryA.getValue());
            }
            transformedMap.put(height, sortedMap);
        }
        return transformedMap;
    }

    /**
     * 计算样本的方差
     *
     * @param data 样本数据
     * @return 方差
     */
    public static double variance(List<Double> data) {
        double mean = mean(data);
        double sumSquaredDeviations = 0;
        for (double datum : data) {
            sumSquaredDeviations += Math.pow(datum - mean, 2);
        }
        return sumSquaredDeviations / (data.size() - 1);
    }

    public static List<String> sort(List<String> data) {
        // 自定义排序
        Collections.sort(data, new Comparator<String>() {
            @Override
            public int compare(String s1, String s2) {
                // 提取字符串中的数字部分
                int num1 = extractNumber(s1);
                int num2 = extractNumber(s2);

                // 比较数字部分
                return Integer.compare(num1, num2);
            }

            // 辅助方法：提取字符串中的数字部分
            private int extractNumber(String str) {
                String numberStr = str.replaceAll("\\D+", ""); // 使用正则表达式去除非数字字符
                return numberStr.isEmpty() ? 0 : Integer.parseInt(numberStr);
            }
        });

        return data;
    }

    public static double calculateRMSD(List<Double> data1, List<Double> data2) {
        if (data1.size() != data2.size()) {
           return 0.0;
        }
        double sumOfSquaresOfDifferences = 0.0;
        for (int i = 0; i < data1.size(); i++) {
            double difference = data1.get(i) - data2.get(i);
            sumOfSquaresOfDifferences += difference * difference;
        }

        return Math.sqrt(sumOfSquaresOfDifferences / data1.size());
    }

    public static double calculateRMSDAdd360(List<Double> data1, List<Double> data2) {
        if (data1.size() != data2.size()) {
            return 0.0;
        }
        double sumOfSquaresOfDifferences = 0.0;
        for (int i = 0; i < data1.size(); i++) {
            double a = data1.get(i); //雷达原始值
            double c = data2.get(i); //风塔原始值
            double b = data1.get(i) + 360; //雷达加360值
            double difference = 0.0;

            double diffA = Math.abs(a - c); //原始值减去风塔的差值
            double diffB = Math.abs(b - c); //新值减去风塔的差值

            if (diffA < diffB) {
                difference = a - c;
            } else if (diffA > diffB) {
                difference = b - c;
            } else {
                difference = a - c;
            }
            sumOfSquaresOfDifferences += difference * difference;
        }
        return Math.sqrt(sumOfSquaresOfDifferences / data1.size());
    }

    /**
     * 计算样本的标准差
     *
     * @param data 样本数据
     * @return 标准差
     */
    public static double stdDev(List<Double> data) {
        return Math.sqrt(variance(data));
    }

    /**
     * 计算样本的平均值
     *
     * @param data 样本数据
     * @return 平均值
     */
    private static double mean(List<Double> data) {
        double sum = 0;
        for (double datum : data) {
            sum += datum;
        }
        return sum / data.size();
    }


    public static class SimpleLinearRegression {
        private double a; // 斜率
        private double b; // 截距

        public SimpleLinearRegression(List<Double> x, List<Double> y) {
            if (x.size() != y.size()) {
                a = 0.0;
                b = 0.0;
            }else {
                double sumX = 0.0;
                double sumY = 0.0;
                double sumXY = 0.0;
                double sumXX = 0.0;
                int n = x.size();
                for (int i = 0; i < n; i++) {

                    double a = x.get(i); //雷达原始值
                    double c = y.get(i); //风塔原始值
                    double b = x.get(i) + 360; //雷达加360值


                    double diffA = Math.abs(a - c); //原始值减去风塔的差值
                    double diffB = Math.abs(b - c); //新值减去风塔的差值
                    if (diffA < diffB) {
                        sumX += a;
                        sumY += c;
                        sumXY += a * c;
                        sumXX += a * a;
                    } else if (diffA > diffB) {
                        sumX += b;
                        sumY += c;
                        sumXY += b * c;
                        sumXX += b * b;
                    } else {
                        sumX += a;
                        sumY += c;
                        sumXY += a * c;
                        sumXX += a * a;
                    }
                }
                a = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
                b = (sumY - a * sumX) / n;
            }
        }

        public double getSlope() {
            return a;
        }

        public double getIntercept() {
            return b;
        }
    }

    // 自定义比较器，用于比较key中包含的数字
    private static int compareKeysByNumbers(Map.Entry<String, Double> entry1, Map.Entry<String, Double> entry2) {
        // 提取key中的数字部分
        int[] numbers1 = extractNumbersFromString(entry1.getKey());
        int[] numbers2 = extractNumbersFromString(entry2.getKey());

        // 比较两个数字数组
        for (int i = 0; i < Math.min(numbers1.length, numbers2.length); i++) {
            int comparison = Integer.compare(numbers1[i], numbers2[i]);
            if (comparison != 0) {
                return comparison;
            }
        }

        // 如果所有对应的数字都相等，则可以根据需要比较其他部分（例如字符串的其余部分）
        // 这里为了简单起见，只比较数字部分，如果数字部分都相同，则保持原始顺序（由LinkedHashMap保证）
        // 如果需要更复杂的比较逻辑，可以在这里添加

        return 0; // 如果所有数字都相同，则返回0表示相等（这里不会改变List的顺序）
    }

    // 使用正则表达式提取字符串中的所有数字部分，并存储在一个数组中
    private static int[] extractNumbersFromString(String str) {
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(str);
        List<Integer> numbers = new ArrayList<>();
        while (matcher.find()) {
            numbers.add(Integer.parseInt(matcher.group()));
        }
        return numbers.stream().mapToInt(Integer::intValue).toArray();
    }

    // 根据高度拆分数据的方法
    private static Map<String, Map<String, Double>> splitByHeight(Map<String, Double> data) {
        Map<String, Map<String, Double>> splitData = new HashMap<>();
        Pattern pattern = Pattern.compile(".*\\/(\\d+)风塔.*"); // 正则表达式用于提取高度信息

        for (Map.Entry<String, Double> entry : data.entrySet()) {
            Matcher matcher = pattern.matcher(entry.getKey());
            if (matcher.find()) {
                int lengthFromEnd = 5;
                int startIndex = Math.max(0, entry.getKey().length() - lengthFromEnd);
                String substringStr = entry.getKey().substring(0, startIndex);
                String height = substringStr; // 提取高度信息
//                String height = matcher.group(1); // 提取高度信息
                splitData.computeIfAbsent(height, k -> new HashMap<>()).put(entry.getKey(), entry.getValue());
            }
        }

        return splitData;
    }
}
