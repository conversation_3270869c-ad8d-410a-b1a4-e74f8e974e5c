package com.zysj.web.controller.status;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.status.SystemAlarmRecord;
import com.zysj.system.service.status.ISystemAlarmRecordService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 设备预警日志Controller
 * 
 * <AUTHOR>
 * @date 2025-02-03
 */
@RestController
@RequestMapping("/system/systemAlarmRecord")
public class SystemAlarmRecordController extends BaseController
{
    @Autowired
    private ISystemAlarmRecordService systemAlarmRecordService;

    /**
     * 查询设备预警日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(SystemAlarmRecord systemAlarmRecord)
    {
        startPage();
        List<SystemAlarmRecord> list = systemAlarmRecordService.getList(systemAlarmRecord);
        return getDataTable(list);
    }

    /**
     * 查询设备预警日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmRecord:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(SystemAlarmRecord systemAlarmRecord)
    {

        List<SystemAlarmRecord> list = systemAlarmRecordService.getList(systemAlarmRecord);
        return getDataTable(list);
    }

    /**
     * 导出设备预警日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmRecord:export')")
    @Log(title = "设备预警日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SystemAlarmRecord systemAlarmRecord)
    {
        List<SystemAlarmRecord> list = systemAlarmRecordService.getList(systemAlarmRecord);
        ExcelUtil<SystemAlarmRecord> util = new ExcelUtil<SystemAlarmRecord>(SystemAlarmRecord.class);
        util.exportExcel(response, list, "设备预警日志数据");
    }

    /**
     * 获取设备预警日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmRecord:query')")
    @GetMapping(value = "/{systemAlarmRecordId}")
    public AjaxResult getInfo(@PathVariable("systemAlarmRecordId") Long systemAlarmRecordId)
    {
        return success(systemAlarmRecordService.getInfo(systemAlarmRecordId));
    }

    /**
     * 新增设备预警日志
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmRecord:add')")
    @Log(title = "设备预警日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SystemAlarmRecord systemAlarmRecord)
    {
        return toAjax(systemAlarmRecordService.insert(systemAlarmRecord));
    }

    /**
     * 修改设备预警日志
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmRecord:edit')")
    @Log(title = "设备预警日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SystemAlarmRecord systemAlarmRecord)
    {
        return toAjax(systemAlarmRecordService.update(systemAlarmRecord));
    }

    /**
     * 删除设备预警日志
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmRecord:remove')")
    @Log(title = "设备预警日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{systemAlarmRecordIds}")
    public AjaxResult remove(@PathVariable Long[] systemAlarmRecordIds)
    {
        return toAjax(systemAlarmRecordService.deleteByIds(systemAlarmRecordIds));
    }
}
