package com.zysj.web.controller.device;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.device.LaserParam;
import com.zysj.system.service.device.ILaserParamService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 工控机激光参数管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-26
 */
@RestController
@RequestMapping("/device/laserParam")
public class LaserParamController extends BaseController
{
    @Autowired
    private ILaserParamService laserParamService;

    /**
     * 查询工控机激光参数管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:param:list')")
    @GetMapping("/list")
    public TableDataInfo list(LaserParam laserParam)
    {
        startPage();
        List<LaserParam> list = laserParamService.getList(laserParam);
        return getDataTable(list);
    }

    /**
     * 查询工控机激光参数管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:param:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(LaserParam laserParam)
    {
        List<LaserParam> list = laserParamService.getList(laserParam);
        return getDataTable(list);
    }

    /**
     * 导出工控机激光参数管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:param:export')")
    @Log(title = "工控机激光参数管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, LaserParam laserParam)
    {
        List<LaserParam> list = laserParamService.getList(laserParam);
        ExcelUtil<LaserParam> util = new ExcelUtil<LaserParam>(LaserParam.class);
        util.exportExcel(response, list, "工控机激光参数管理数据");
    }

    /**
     * 获取工控机激光参数管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:param:query')")
    @GetMapping(value = "/{laserParamId}")
    public AjaxResult getInfo(@PathVariable("laserParamId") Long laserParamId)
    {
        return success(laserParamService.getInfo(laserParamId));
    }

    /**
     * 新增工控机激光参数管理
     */
    @PreAuthorize("@ss.hasPermi('system:param:add')")
    @Log(title = "工控机激光参数管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LaserParam laserParam)
    {
        return toAjax(laserParamService.insert(laserParam));
    }

    /**
     * 修改工控机激光参数管理
     */
    @PreAuthorize("@ss.hasPermi('system:param:edit')")
    @Log(title = "工控机激光参数管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LaserParam laserParam)
    {
        return toAjax(laserParamService.update(laserParam));
    }

    /**
     * 删除工控机激光参数管理
     */
    @PreAuthorize("@ss.hasPermi('system:param:remove')")
    @Log(title = "工控机激光参数管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{laserParamIds}")
    public AjaxResult remove(@PathVariable Long[] laserParamIds)
    {
        return toAjax(laserParamService.deleteByIds(laserParamIds));
    }

    /**
     * 启动激光器
     */
    @PreAuthorize("@ss.hasPermi('system:param:start')")
    @Log(title = "启动激光器", businessType = BusinessType.PUSH)
    @PostMapping("/start")
    public AjaxResult start(@RequestBody LaserParam laserParam) throws Exception {
        return toAjax(laserParamService.start(laserParam));
    }

    /**
     * 关闭激光器
     */
    @PreAuthorize("@ss.hasPermi('system:param:close')")
    @Log(title = "启动激光器", businessType = BusinessType.PUSH)
    @PostMapping("/close")
    public AjaxResult close(@RequestBody LaserParam laserParam) throws Exception {
        return toAjax(laserParamService.close(laserParam));
    }
}
