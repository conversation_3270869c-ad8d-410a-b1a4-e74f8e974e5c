package com.zysj.web.controller.data;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class MergeCSVToExcel {

    public static void main(String[] args) throws IOException, ParseException {
        // 定义文件路径
        String[] windSpeedFiles = {"平均水平风速_20241229.csv", "平均水平风速_20250214.csv"};
        String[] windDirectionFiles = {"平均水平风向_20250214.csv"};
        String outputExcelFile = "雷达数据汇总.xlsx";

        // 读取所有风速和风向数据
        List<Map<String, String>> windSpeedData = readCSVFiles(windSpeedFiles);
        List<Map<String, String>> windDirectionData = readCSVFiles(windDirectionFiles);

        // 合并数据
        List<Map<String, String>> mergedData = mergeData(windSpeedData, windDirectionData);

        // 写入Excel文件
        writeToExcel(mergedData, outputExcelFile);
    }

    private static List<Map<String, String>> readCSVFiles(String[] filePaths) throws IOException, ParseException {
        List<Map<String, String>> data = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");

        for (String filePath : filePaths) {
            try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
                String line;
                String[] headers = null;

                while ((line = br.readLine()) != null) {
                    String[] values = line.split(",");
                    if (headers == null) {
                        headers = values;
                    } else {
                        Map<String, String> row = new HashMap<>();
                        for (int i = 0; i < headers.length; i++) {
                            row.put(headers[i], values[i]);
                        }
                        data.add(row);
                    }
                }
            }
        }

        // 按时间排序
        data.sort((row1, row2) -> {
            try {
                Date date1 = dateFormat.parse(row1.get("Date Time"));
                Date date2 = dateFormat.parse(row2.get("Date Time"));
                return date1.compareTo(date2);
            } catch (ParseException e) {
                e.printStackTrace();
                return 0;
            }
        });

        return data;
    }

    private static List<Map<String, String>> mergeData(List<Map<String, String>> windSpeedData, List<Map<String, String>> windDirectionData) {
        List<Map<String, String>> mergedData = new ArrayList<>();

        for (Map<String, String> speedRow : windSpeedData) {
            String dateTime = speedRow.get("Date Time");
            Map<String, String> directionRow = findRowByDateTime(windDirectionData, dateTime);

            if (directionRow != null) {
                Map<String, String> mergedRow = new HashMap<>(speedRow);
                mergedRow.putAll(directionRow);
                mergedData.add(mergedRow);
            }
        }

        return mergedData;
    }

    private static Map<String, String> findRowByDateTime(List<Map<String, String>> data, String dateTime) {
        for (Map<String, String> row : data) {
            if (row.get("Date Time").equals(dateTime)) {
                return row;
            }
        }
        return null;
    }

    private static void writeToExcel(List<Map<String, String>> data, String outputFile) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Sheet1");

        // 创建表头
        Row headerRow = sheet.createRow(0);
        Set<String> headers = data.get(0).keySet();
        int colNum = 0;
        for (String header : headers) {
            Cell cell = headerRow.createCell(colNum++);
            cell.setCellValue(header);
        }

        // 填充数据
        int rowNum = 1;
        for (Map<String, String> row : data) {
            Row excelRow = sheet.createRow(rowNum++);
            colNum = 0;
            for (String header : headers) {
                Cell cell = excelRow.createCell(colNum++);
                cell.setCellValue(row.get(header));
            }
        }

        // 写入文件
        try (FileOutputStream fileOut = new FileOutputStream(outputFile)) {
            workbook.write(fileOut);
        }

        workbook.close();
    }
}