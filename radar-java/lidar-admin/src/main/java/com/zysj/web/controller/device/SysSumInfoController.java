package com.zysj.web.controller.device;

import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.system.service.device.ISysSumInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 雷达设备状态Controller
 *
 * <AUTHOR>
 * @date 2025-03-11
 */
@Api(tags = "雷达设备物理状态")
@RestController
@RequestMapping("/device/sysinfo")
public class SysSumInfoController extends BaseController {

    @Resource
    private ISysSumInfoService iSysSumInfoService;

    /**
     * 雷达设备
     */
    @ApiOperation(value = "雷达设备信息")
    @GetMapping(value = "/getMonitorStatus")
    public AjaxResult getMonitorStatus() {
        return success(iSysSumInfoService.getMonitorStatus());
    }

}
