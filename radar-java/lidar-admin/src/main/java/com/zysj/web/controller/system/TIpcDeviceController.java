package com.zysj.web.controller.system;

import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.core.page.TableDataInfo;
import com.zysj.common.enums.BusinessType;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.system.domain.TIpcDevice;
import com.zysj.system.service.ITIpcDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 工控机管理Controller
 * 
 * <AUTHOR>
 * @date 2024-10-17
 */
@Api(tags="工控机管理")
@RestController
@RequestMapping("/system/ipcdevice")
public class TIpcDeviceController extends BaseController
{
    @Autowired
    private ITIpcDeviceService tIpcDeviceService;

    /**
     * 查询工控机管理列表
     */
    @ApiOperation(value="查询工控机管理列表")
    @PreAuthorize("@ss.hasPermi('system:device:list')")
    @GetMapping("/list")
    public TableDataInfo list(TIpcDevice tIpcDevice)
    {
        startPage();
        List<TIpcDevice> list = tIpcDeviceService.selectTIpcDeviceList(tIpcDevice);
        return getDataTable(list);
    }

    /**
     * 导出工控机管理列表
     */
    @ApiOperation(value="导出工控机管理列表")
    @PreAuthorize("@ss.hasPermi('system:device:export')")
    @Log(title = "工控机管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TIpcDevice tIpcDevice)
    {
        List<TIpcDevice> list = tIpcDeviceService.selectTIpcDeviceList(tIpcDevice);
        ExcelUtil<TIpcDevice> util = new ExcelUtil<TIpcDevice>(TIpcDevice.class);
        util.exportExcel(response, list, "工控机管理数据");
    }

    /**
     * 获取工控机管理详细信息
     */
    @ApiOperation(value="获取工控机管理详细信息")
    @PreAuthorize("@ss.hasPermi('system:device:query')")
    @GetMapping(value = "/{tIpcDeviceId}")
    public AjaxResult getInfo(@PathVariable("tIpcDeviceId") Long tIpcDeviceId)
    {
        return success(tIpcDeviceService.selectTIpcDeviceByTIpcDeviceId(tIpcDeviceId));
    }

    /**
     * 新增工控机管理
     */
    @ApiOperation(value="新增工控机管理")
    @PreAuthorize("@ss.hasPermi('system:device:add')")
    @Log(title = "工控机管理", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResult add(@RequestBody TIpcDevice tIpcDevice)
    {
        return toAjax(tIpcDeviceService.insertTIpcDevice(tIpcDevice));
    }

    /**
     * 修改工控机管理
     */
    @ApiOperation(value="修改工控机管理")
    @PreAuthorize("@ss.hasPermi('system:device:edit')")
    @Log(title = "工控机管理", businessType = BusinessType.UPDATE)
    @PutMapping("edit")
    public AjaxResult edit(@RequestBody TIpcDevice tIpcDevice)
    {
        return toAjax(tIpcDeviceService.updateTIpcDevice(tIpcDevice));
    }

    /**
     * 删除工控机管理
     */
    @ApiOperation(value="删除工控机管理")
    @PreAuthorize("@ss.hasPermi('system:device:remove')")
    @Log(title = "工控机管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{tIpcDeviceIds}")
    public AjaxResult remove(@PathVariable Long[] tIpcDeviceIds)
    {
        return toAjax(tIpcDeviceService.deleteTIpcDeviceByTIpcDeviceIds(tIpcDeviceIds));
    }
}
