package com.zysj.web.controller.maintenance;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.maintenance.MaintenRecord;
import com.zysj.system.service.maintenance.IMaintenRecordService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 维修管理Controller
 * 
 * <AUTHOR>
 * @date 2025-02-03
 */
@RestController
@RequestMapping("/maintenance/maintenRecord")
public class MaintenRecordController extends BaseController
{
    @Autowired
    private IMaintenRecordService maintenRecordService;

    /**
     * 查询维修管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:maintenRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(MaintenRecord maintenRecord)
    {
        startPage();
        List<MaintenRecord> list = maintenRecordService.getList(maintenRecord);
        return getDataTable(list);
    }

    /**
     * 查询维修管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:maintenRecord:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(MaintenRecord maintenRecord)
    {

        List<MaintenRecord> list = maintenRecordService.getList(maintenRecord);
        return getDataTable(list);
    }

    /**
     * 导出维修管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:maintenRecord:export')")
    @Log(title = "维修管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MaintenRecord maintenRecord)
    {
        List<MaintenRecord> list = maintenRecordService.getList(maintenRecord);
        ExcelUtil<MaintenRecord> util = new ExcelUtil<MaintenRecord>(MaintenRecord.class);
        util.exportExcel(response, list, "维修管理数据");
    }

    /**
     * 获取维修管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:maintenRecord:query')")
    @GetMapping(value = "/{maintenRecordId}")
    public AjaxResult getInfo(@PathVariable("maintenRecordId") Long maintenRecordId)
    {
        return success(maintenRecordService.getInfo(maintenRecordId));
    }

    /**
     * 新增维修管理
     */
    @PreAuthorize("@ss.hasPermi('system:maintenRecord:add')")
    @Log(title = "维修管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MaintenRecord maintenRecord)
    {
        return toAjax(maintenRecordService.insert(maintenRecord));
    }

    /**
     * 修改维修管理
     */
    @PreAuthorize("@ss.hasPermi('system:maintenRecord:edit')")
    @Log(title = "维修管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MaintenRecord maintenRecord)
    {
        return toAjax(maintenRecordService.update(maintenRecord));
    }

    /**
     * 删除维修管理
     */
    @PreAuthorize("@ss.hasPermi('system:maintenRecord:remove')")
    @Log(title = "维修管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{maintenRecordIds}")
    public AjaxResult remove(@PathVariable Long[] maintenRecordIds)
    {
        return toAjax(maintenRecordService.deleteByIds(maintenRecordIds));
    }
}
