//package com.zysj.web.controller.config;
//
//import org.springframework.beans.factory.annotation.Configurable;
//import org.springframework.beans.factory.annotation.Value;
//
//@Configurable
//public class WebExUrl {
//
//    @Value("ladar.ip")
//    public static String ladarIp;
//
//    @Value("CaptureCard.InitSerialPort")
//    public static String CaptureCard_InitSerialPort;
//
//    @Value("CaptureCard.SerialPortOpen")
//    public static String CaptureCard_SerialPortOpen;
//
//    @Value("CaptureCard.SerialPortClose")
//    public static String CaptureCard_SerialPortClose;
//
//    @Value("Laser.InitSerialPort")
//    public static String Laser_InitSerialPort;
//
//    @Value("Laser.SerialPortOpen")
//    public static String Laser_SerialPortOpen;
//
//    @Value("Laser.SerialPortClose")
//    public static String Laser_SerialPortClose;
//    @Value("Laser.OpenLaser")
//    public static String Laser_OpenLaser;
//
//    @Value("Laser.ReadParam")
//    public static String Laser_ReadParam;
//
//    @Value("Laser.CloseLaser")
//    public static String Laser_CloseLaser;
//
//    @Value("Turntable.InitSerialPort")
//    public static String Turntable_InitSerialPort;
//
//    @Value("Turntable.SerialPortOpen")
//    public static String Turntable_SerialPortOpen;
//
//    @Value("Turntable.SerialPortClose")
//    public static String Turntable_SerialPortClose;
//
//    @Value("Turntable.SetParam")
//    public static String Turntable_SetParam;
//
//
//    @Value("THSensor.InitSerialPort")
//    public static String THSensor_InitSerialPort;
//
//    @Value("THSensor.SerialPortOpen")
//    public static String THSensor_SerialPortOpen;
//
//    @Value("THSensor.SerialPortClose")
//    public static String THSensor_SerialPortClose;
//
//
//    @Value("ScanScheme.write")
//    public static String ScanScheme_Write;
//
//    @Value("ScanScheme.read")
//    public static String ScanScheme_Read;
//
//    @Value("WindLidar.init")
//    public static String WindLidar_Init;
//
//    @Value("WindLidar.start")
//    public static String WindLidar_Start;
//
//    @Value("WindLidar.stop")
//    public static String WindLidar_Stop;
//
//    @Value("WindLidar.info")
//    public static String WindLidar_Info;
//
//    @Value("WindLidar.status")
//    public static String WindLidar_Status;
//
//    @Value("WindowsOS.getNetworkList")
//    public static String WindowsOS_GetNetworkList;
//
//    @Value("WindowsOS.getSysInfo")
//    public static String WindowsOS_GetSysInfo;
//
//
//
//
//}
