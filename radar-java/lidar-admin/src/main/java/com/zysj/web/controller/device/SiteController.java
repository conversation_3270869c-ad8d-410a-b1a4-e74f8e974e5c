package com.zysj.web.controller.device;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.device.Site;
import com.zysj.system.service.device.ISiteService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 站点管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-26
 */
@RestController
@RequestMapping("/device/site")
public class SiteController extends BaseController
{
    @Autowired
    private ISiteService siteService;

    /**
     * 查询站点管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:site:list')")
    @GetMapping("/list")
    public TableDataInfo list(Site site)
    {
        startPage();
        List<Site> list = siteService.getList(site);
        return getDataTable(list);
    }

    /**
     * 查询站点管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:site:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(Site site)
    {
        List<Site> list = siteService.getList(site);
        return getDataTable(list);
    }

    /**
     * 导出站点管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:site:export')")
    @Log(title = "站点管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Site site)
    {
        List<Site> list = siteService.getList(site);
        ExcelUtil<Site> util = new ExcelUtil<Site>(Site.class);
        util.exportExcel(response, list, "站点管理数据");
    }

    /**
     * 获取站点管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:site:query')")
    @GetMapping(value = "/{siteId}")
    public AjaxResult getInfo(@PathVariable(value = "siteId",required = false) Long siteId)
    {
        return success(siteService.getInfo(siteId));
    }

    /**
     * 新增站点管理
     */
    @PreAuthorize("@ss.hasPermi('system:site:add')")
    @Log(title = "站点管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Site site)
    {
        return toAjax(siteService.insert(site));
    }

    /**
     * 修改站点管理
     */
    @PreAuthorize("@ss.hasPermi('system:site:edit')")
    @Log(title = "站点管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Site site)
    {
        return toAjax(siteService.update(site));
    }

    /**
     * 删除站点管理
     */
    @PreAuthorize("@ss.hasPermi('system:site:remove')")
    @Log(title = "站点管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{siteIds}")
    public AjaxResult remove(@PathVariable Long[] siteIds)
    {
        return toAjax(siteService.deleteByIds(siteIds));
    }

    /**
     * 站点信息推送
     */
    @PreAuthorize("@ss.hasPermi('system:site:push')")
    @Log(title = "站点信息推送",businessType = BusinessType.PUSH)
    @PostMapping("/batchPushSiteInfos")
    public AjaxResult batchPushSiteInfos(@RequestBody Site site) throws Exception {
        return toAjax(siteService.batchPushSiteInfos(site));
    }
}
