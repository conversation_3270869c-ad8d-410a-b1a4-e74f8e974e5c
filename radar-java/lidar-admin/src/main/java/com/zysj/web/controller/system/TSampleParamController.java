package com.zysj.web.controller.system;

import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.core.page.TableDataInfo;
import com.zysj.common.enums.BusinessType;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.system.domain.TSampleParam;
import com.zysj.system.service.ITSampleParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 工控机采集卡参数管理Controller
 * 
 * <AUTHOR>
 * @date 2024-10-17
 */
@Api(tags="工控机采集卡参数管理")
@RestController
@RequestMapping("/system/sampleparam")
public class TSampleParamController extends BaseController
{
    @Autowired
    private ITSampleParamService tSampleParamService;

    /**
     * 查询工控机采集卡参数管理列表
     */
    @ApiOperation(value="查询工控机采集卡参数管理列表")
    @PreAuthorize("@ss.hasPermi('system:param:list')")
    @GetMapping("/list")
    public TableDataInfo list(TSampleParam tSampleParam)
    {
        startPage();
        List<TSampleParam> list = tSampleParamService.selectTSampleParamList(tSampleParam);
        return getDataTable(list);
    }

    /**
     * 导出工控机采集卡参数管理列表
     */
    @ApiOperation(value="导出工控机采集卡参数管理列表")
    @PreAuthorize("@ss.hasPermi('system:param:export')")
    @Log(title = "工控机采集卡参数管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TSampleParam tSampleParam)
    {
        List<TSampleParam> list = tSampleParamService.selectTSampleParamList(tSampleParam);
        ExcelUtil<TSampleParam> util = new ExcelUtil<TSampleParam>(TSampleParam.class);
        util.exportExcel(response, list, "工控机采集卡参数管理数据");
    }

    /**
     * 获取工控机采集卡参数管理详细信息
     */
    @ApiOperation(value="获取工控机采集卡参数管理详细信息")
    @PreAuthorize("@ss.hasPermi('system:param:query')")
    @GetMapping(value = "/{tSampleParamId}")
    public AjaxResult getInfo(@PathVariable("tSampleParamId") Long tSampleParamId)
    {
        return success(tSampleParamService.selectTSampleParamByTSampleParamId(tSampleParamId));
    }

    /**
     * 新增工控机采集卡参数管理
     */
    @ApiOperation(value="新增工控机采集卡参数管理")
    @PreAuthorize("@ss.hasPermi('system:param:add')")
    @Log(title = "工控机采集卡参数管理", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResult add(@RequestBody TSampleParam tSampleParam)
    {
        return toAjax(tSampleParamService.insertTSampleParam(tSampleParam));
    }

    /**
     * 修改工控机采集卡参数管理
     */
    @ApiOperation(value="修改工控机采集卡参数管理")
    @PreAuthorize("@ss.hasPermi('system:param:edit')")
    @Log(title = "工控机采集卡参数管理", businessType = BusinessType.UPDATE)
    @PutMapping("edit")
    public AjaxResult edit(@RequestBody TSampleParam tSampleParam)
    {
        return toAjax(tSampleParamService.updateTSampleParam(tSampleParam));
    }

    /**
     * 删除工控机采集卡参数管理
     */
    @ApiOperation(value="删除工控机采集卡参数管理")
    @PreAuthorize("@ss.hasPermi('system:param:remove')")
    @Log(title = "工控机采集卡参数管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{tSampleParamIds}")
    public AjaxResult remove(@PathVariable Long[] tSampleParamIds)
    {
        return toAjax(tSampleParamService.deleteTSampleParamByTSampleParamIds(tSampleParamIds));
    }
}
