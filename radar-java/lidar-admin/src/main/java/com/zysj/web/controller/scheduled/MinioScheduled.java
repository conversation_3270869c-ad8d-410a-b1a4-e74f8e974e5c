package com.zysj.web.controller.scheduled;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zysj.common.utils.MinioUtil;
import com.zysj.system.domain.file.FileConfig;
import com.zysj.system.domain.file.MinioFiles;
import com.zysj.system.enums.FileConfigEnum;
import com.zysj.system.service.file.FileConfigService;
import com.zysj.system.service.file.MinioFilesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class MinioScheduled {
    @Resource
    private FileConfigService fileConfigService;
    @Resource
    private MinioFilesService minioFilesService;
    @Resource
    private MinioUtil minioUtil;

    /**
     * 根据配置信息，定时删除过期文件
     */
    @Scheduled(cron = "0 0 0/3 * * ?")
    public void scheduled() {
        log.info("开始执行Minio文件删除任务");
        LambdaQueryWrapper<FileConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileConfig::getConfigType, FileConfigEnum.SYSTEM);
        Optional.ofNullable(fileConfigService.getOne(queryWrapper)).ifPresent(this::deleteMinioFiles);
    }

    private void deleteMinioFiles(FileConfig fileConfig) {
        LocalDateTime time = LocalDateTime.now().minusYears(fileConfig.getYears()).minusMonths(fileConfig.getMonths()).minusDays(fileConfig.getDays());
        minioFilesService.list(new LambdaQueryWrapper<MinioFiles>().lt(MinioFiles::getUploadTime, time)).forEach(minioFile -> {
            minioUtil.delete(minioFile.getBucketName(), minioFile.getObjectName());
            minioFilesService.removeById(minioFile.getId());
            log.info("文件：{} 已被删除", minioFile.getObjectName());
        });
    }
}
