package com.zysj.web.controller.file;

import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.system.domain.file.FileConfig;
import com.zysj.system.domain.vo.FileConfigVo;
import com.zysj.system.service.file.FileConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件配置Controller
 */
@RestController
@RequestMapping("/fileConfig")
public class FileConfigController extends BaseController {
    @Resource
    private FileConfigService fileConfigService;

    @PostMapping("/save")
    public AjaxResult save(@RequestBody FileConfig fileConfig) {
        fileConfigService.saveOrUpdate(fileConfig);
        return success();
    }

    @GetMapping("/list")
    public AjaxResult list() {
        List<FileConfigVo> result = fileConfigService.list().stream()
                .map(fileConfig -> {
                    FileConfigVo vo = new FileConfigVo();
                    BeanUtils.copyProperties(fileConfig, vo);
                    return vo;
                })
                .collect(Collectors.toList());

        return success(result);
    }
}
