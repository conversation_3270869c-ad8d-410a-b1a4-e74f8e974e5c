package com.zysj.web.controller.task;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.core.page.TableDataInfo;
import com.zysj.system.domain.TaskRecord;
import com.zysj.system.domain.TaskRecordDetail;
import com.zysj.system.domain.dto.TaskRecordDTO;
import com.zysj.system.service.task.TaskRecordService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/taskRecord")
public class TaskRecordController extends BaseController {
    @Resource
    private TaskRecordService taskRecordService;

    @PostMapping("/save")
    public AjaxResult save(@RequestBody TaskRecordDTO taskRecordDTO) {
        taskRecordService.add(taskRecordDTO);
        return success();
    }

    @PostMapping("/pageList")
    public TableDataInfo list(@RequestBody TaskRecordDTO taskRecordDTO) {
        List<TaskRecord> list = taskRecordService.pageList(taskRecordDTO);
        return getDataTable(list);
    }

    @GetMapping("/detail/{taskSunId}")
    public AjaxResult detail(@PathVariable String taskSunId) {
        TaskRecordDetail result = taskRecordService.detail(taskSunId);
        return success(result);
    }
}
