package com.zysj.web.controller.common;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.common.CommParam;
import com.zysj.system.service.common.ICommParamService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2025-02-06
 */
@RestController
@RequestMapping("/system/commParam")
public class CommParamController extends BaseController
{
    @Autowired
    private ICommParamService commParamService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:commParam:list')")
    @GetMapping("/list")
    public TableDataInfo list(CommParam commParam)
    {
        startPage();
        List<CommParam> list = commParamService.getList(commParam);
        return getDataTable(list);
    }
    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:commParam:list')")
    @GetMapping("/listAll")
    public AjaxResult getAllList(CommParam commParam)
    {
        List<CommParam> list = commParamService.getList(commParam);
        return success(list);
    }


    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:commParam:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CommParam commParam)
    {
        List<CommParam> list = commParamService.getList(commParam);
        ExcelUtil<CommParam> util = new ExcelUtil<CommParam>(CommParam.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:commParam:query')")
    @GetMapping(value = "/{commParamId}")
    public AjaxResult getInfo(@PathVariable("commParamId") Long commParamId)
    {
        return success(commParamService.getInfo(commParamId));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:commParam:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CommParam commParam)
    {
        return toAjax(commParamService.insert(commParam));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:commParam:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CommParam commParam)
    {
        return toAjax(commParamService.update(commParam));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:commParam:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{commParamIds}")
    public AjaxResult remove(@PathVariable Long[] commParamIds)
    {
        return toAjax(commParamService.deleteByIds(commParamIds));
    }
}
