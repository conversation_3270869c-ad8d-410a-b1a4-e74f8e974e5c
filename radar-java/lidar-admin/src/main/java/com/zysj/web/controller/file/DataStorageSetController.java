package com.zysj.web.controller.file;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.file.DataStorageSet;
import com.zysj.system.service.file.IDataStorageSetService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 数据存储设置Controller
 * 
 * <AUTHOR>
 * @date 2025-02-03
 */
@RestController
@RequestMapping("/file/dataStorageSet")
public class DataStorageSetController extends BaseController
{
    @Autowired
    private IDataStorageSetService dataStorageSetService;

    /**
     * 查询数据存储设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataStorageSet:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataStorageSet dataStorageSet)
    {
        startPage();
        List<DataStorageSet> list = dataStorageSetService.getList(dataStorageSet);
        return getDataTable(list);
    }

    /**
     * 查询数据存储设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataStorageSet:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(DataStorageSet dataStorageSet)
    {

        List<DataStorageSet> list = dataStorageSetService.getList(dataStorageSet);
        return getDataTable(list);
    }
    /**
     * 导出数据存储设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataStorageSet:export')")
    @Log(title = "数据存储设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataStorageSet dataStorageSet)
    {
        List<DataStorageSet> list = dataStorageSetService.getList(dataStorageSet);
        ExcelUtil<DataStorageSet> util = new ExcelUtil<DataStorageSet>(DataStorageSet.class);
        util.exportExcel(response, list, "数据存储设置数据");
    }

    /**
     * 获取数据存储设置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dataStorageSet:query')")
    @GetMapping(value = "/{dataStorageSetId}")
    public AjaxResult getInfo(@PathVariable("dataStorageSetId") Long dataStorageSetId)
    {
        return success(dataStorageSetService.getInfo(dataStorageSetId));
    }

    /**
     * 新增数据存储设置
     */
    @PreAuthorize("@ss.hasPermi('system:dataStorageSet:add')")
    @Log(title = "数据存储设置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataStorageSet dataStorageSet)
    {
        return toAjax(dataStorageSetService.insert(dataStorageSet));
    }

    /**
     * 修改数据存储设置
     */
    @PreAuthorize("@ss.hasPermi('system:dataStorageSet:edit')")
    @Log(title = "数据存储设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataStorageSet dataStorageSet)
    {
        return toAjax(dataStorageSetService.update(dataStorageSet));
    }

    /**
     * 删除数据存储设置
     */
    @PreAuthorize("@ss.hasPermi('system:dataStorageSet:remove')")
    @Log(title = "数据存储设置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dataStorageSetIds}")
    public AjaxResult remove(@PathVariable Long[] dataStorageSetIds)
    {
        return toAjax(dataStorageSetService.deleteByIds(dataStorageSetIds));
    }
}
