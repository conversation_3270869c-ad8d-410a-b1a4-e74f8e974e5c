package com.zysj.web.controller.device;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.device.Software;
import com.zysj.system.service.device.ISoftwareService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 软件管理Controller
 * 
 * <AUTHOR>
 * @date 2025-02-03
 */
@RestController
@RequestMapping("/device/software")
public class SoftwareController extends BaseController
{
    @Autowired
    private ISoftwareService softwareService;

    /**
     * 查询软件管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:software:list')")
    @GetMapping("/list")
    public TableDataInfo list(Software software)
    {
        startPage();
        List<Software> list = softwareService.getList(software);
        return getDataTable(list);
    }

    /**
     * 查询软件管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:software:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(Software software)
    {
        List<Software> list = softwareService.getList(software);
        return getDataTable(list);
    }

    /**
     * 导出软件管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:software:export')")
    @Log(title = "软件管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Software software)
    {
        List<Software> list = softwareService.getList(software);
        ExcelUtil<Software> util = new ExcelUtil<Software>(Software.class);
        util.exportExcel(response, list, "软件管理数据");
    }

    /**
     * 获取软件管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:software:query')")
    @GetMapping(value = "/{softwareId}")
    public AjaxResult getInfo(@PathVariable("softwareId") Long softwareId)
    {
        return success(softwareService.getInfo(softwareId));
    }

    /**
     * 新增软件管理
     */
    @PreAuthorize("@ss.hasPermi('system:software:add')")
    @Log(title = "软件管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Software software)
    {
        return toAjax(softwareService.insert(software));
    }

    /**
     * 修改软件管理
     */
    @PreAuthorize("@ss.hasPermi('system:software:edit')")
    @Log(title = "软件管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Software software)
    {
        return toAjax(softwareService.update(software));
    }

    /**
     * 删除软件管理
     */
    @PreAuthorize("@ss.hasPermi('system:software:remove')")
    @Log(title = "软件管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{softwareIds}")
    public AjaxResult remove(@PathVariable Long[] softwareIds)
    {
        return toAjax(softwareService.deleteByIds(softwareIds));
    }
}
