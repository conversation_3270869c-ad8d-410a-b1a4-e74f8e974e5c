package com.zysj.web.controller.device;

import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.core.page.TableDataInfo;
import com.zysj.common.enums.BusinessType;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.system.domain.device.Turntable;
import com.zysj.system.domain.device.TurntableParam;
import com.zysj.system.service.device.ITurntableService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 转台管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-26
 */
@RestController
@RequestMapping("/device/turntable")
public class TurntableController extends BaseController {
    @Autowired
    private ITurntableService turntableService;



    /**
     * 查询转台管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:turntable:list')")
    @GetMapping("/list")
    public TableDataInfo list(Turntable turntable) {
        startPage();
        List<Turntable> list = turntableService.getList(turntable);
        return getDataTable(list);
    }

    /**
     * 查询转台管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:turntable:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(Turntable turntable) {
        List<Turntable> list = turntableService.getList(turntable);
        return getDataTable(list);
    }


    /**
     * 导出转台管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:turntable:export')")
    @Log(title = "转台管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Turntable turntable) {
        List<Turntable> list = turntableService.getList(turntable);
        ExcelUtil<Turntable> util = new ExcelUtil<Turntable>(Turntable.class);
        util.exportExcel(response, list, "转台管理数据");
    }

    /**
     * 获取转台管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:turntable:query')")
    @GetMapping(value = "/{turntableId}")
    public AjaxResult getInfo(@PathVariable("turntableId") Long turntableId) {
        return success(turntableService.getInfo(turntableId));
    }

    /**
     * 获取状态
     */
    @PreAuthorize("@ss.hasPermi('system:turntable:query')")
    @GetMapping(value = "/getMonitorStatus")
    public AjaxResult getMonitorStatus() {
        return success(turntableService.getMonitorStatus());
    }

    /**
     * 新增转台管理
     */
    @PreAuthorize("@ss.hasPermi('system:turntable:add')")
    @Log(title = "转台管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Turntable turntable) {
        return toAjax(turntableService.insert(turntable));
    }

    /**
     * 修改转台管理
     */
    @PreAuthorize("@ss.hasPermi('system:turntable:edit')")
    @Log(title = "转台管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Turntable turntable) {
        return toAjax(turntableService.update(turntable));
    }

    /**
     * 删除转台管理
     */
    @PreAuthorize("@ss.hasPermi('system:turntable:remove')")
    @Log(title = "转台管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{turntableIds}")
    public AjaxResult remove(@PathVariable Long[] turntableIds) {
        return toAjax(turntableService.deleteByIds(turntableIds));
    }


    /**
     * 转台转动
     */
    @PreAuthorize("@ss.hasPermi('system:turntable:remove')")
    @Log(title = "转台管理", businessType = BusinessType.UPDATE)
    @PutMapping("/setParam")
    public AjaxResult setParam(@RequestBody TurntableParam turntableParam)
    {
        return toAjax(turntableService.SetParam(turntableParam));
    }


}
