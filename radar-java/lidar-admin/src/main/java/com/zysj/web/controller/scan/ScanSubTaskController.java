package com.zysj.web.controller.scan;

import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.core.page.TableDataInfo;
import com.zysj.common.enums.BusinessType;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.system.domain.scan.ScanSubTask;
import com.zysj.system.service.scan.IScanSubTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 子任务管理Controller
 *
 * <AUTHOR>
 * @date 2025-01-26
 */
@RestController
@RequestMapping("/scan/scanSubTask")
public class ScanSubTaskController extends BaseController {
    @Autowired
    private IScanSubTaskService scanSubTaskService;

    /**
     * 查询子任务管理列表
     */
    @PreAuthorize("@ss.hasPermi('scan:task:list')")
    @GetMapping("/list")
    public TableDataInfo list(ScanSubTask scanSubTask) {
        startPage();
        List<ScanSubTask> list = scanSubTaskService.getList(scanSubTask);
        return getDataTable(list);
    }

    /**
     * 查询子任务管理列表
     */
    @PreAuthorize("@ss.hasPermi('scan:task:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(ScanSubTask scanSubTask) {
        List<ScanSubTask> list = scanSubTaskService.getList(scanSubTask);
        return getDataTable(list);
    }

    /**
     * 导出子任务管理列表
     */
    @PreAuthorize("@ss.hasPermi('scan:task:export')")
    @Log(title = "子任务管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScanSubTask scanSubTask) {
        List<ScanSubTask> list = scanSubTaskService.getList(scanSubTask);
        ExcelUtil<ScanSubTask> util = new ExcelUtil<ScanSubTask>(ScanSubTask.class);
        util.exportExcel(response, list, "子任务管理数据");
    }

    /**
     * 获取子任务管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('scan:task:query')")
    @GetMapping(value = "/{scanSubTaskId}")
    public AjaxResult getInfo(@PathVariable("scanSubTaskId") String scanSubTaskId) {

        return success(scanSubTaskService.getInfo(scanSubTaskId));
    }

    /**
     * 新增子任务管理
     */
    @PreAuthorize("@ss.hasPermi('scan:task:add')")
    @Log(title = "子任务管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ScanSubTask scanSubTask) {
        return toAjax(scanSubTaskService.insert(scanSubTask));
    }

    /**
     * 修改子任务管理
     */
    @PreAuthorize("@ss.hasPermi('scan:task:edit')")
    @Log(title = "子任务管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ScanSubTask scanSubTask) {
        return toAjax(scanSubTaskService.update(scanSubTask));
    }

    /**
     * 删除子任务管理
     */
    @PreAuthorize("@ss.hasPermi('scan:task:remove')")
    @Log(title = "子任务管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{scanSubTaskIds}")
    public AjaxResult remove(@PathVariable String[] scanSubTaskIds) {
        return toAjax(scanSubTaskService.deleteByIds(scanSubTaskIds));
    }

    /**
     * 复制任务
     */
    @PreAuthorize("@ss.hasPermi('scan:task:edit')")
    @Log(title = "复制任务", businessType = BusinessType.UPDATE)
    @PostMapping("/copyTasks/{scanSubTaskIds}")
    public AjaxResult copyTask(@PathVariable String[] scanSubTaskIds) {
        return toAjax(scanSubTaskService.copyTasks(scanSubTaskIds));
    }

//    /**
//     * 开始任务
//     */
//    @PreAuthorize("@ss.hasPermi('scan:task:start')")
//    @GetMapping("/start")
//    public AjaxResult start(ScanTask scanTask)
//    {
//        return toAjax(scanTaskService.start(scanTask));
//    }
//
//    /**
//     * 结束任务
//     */
//    @PreAuthorize("@ss.hasPermi('scan:task:stop')")
//    @GetMapping("/stop")
//    public AjaxResult stop(ScanTask scanTask)
//    {
//        return toAjax(scanTaskService.stop(scanTask));
//    }

}
