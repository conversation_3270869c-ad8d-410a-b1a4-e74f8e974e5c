package com.zysj.web.controller.system;

import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.core.page.TableDataInfo;
import com.zysj.common.enums.BusinessType;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.system.domain.TRadarDevice;
import com.zysj.system.service.ITRadarDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 雷达设备管理Controller
 * 
 * <AUTHOR>
 * @date 2024-10-17
 */
@Api(tags="雷达设备管理")
@RestController
@RequestMapping("/system/radardevice")
public class TRadarDeviceController extends BaseController
{
    @Autowired
    private ITRadarDeviceService tRadarDeviceService;

    /**
     * 查询雷达设备管理列表
     */
    @ApiOperation(value="查询雷达设备管理列表")
    @PreAuthorize("@ss.hasPermi('system:device:list')")
    @GetMapping("/list")
    public TableDataInfo list(TRadarDevice tRadarDevice)
    {
        startPage();
        List<TRadarDevice> list = tRadarDeviceService.selectTRadarDeviceList(tRadarDevice);
        return getDataTable(list);
    }

    /**
     * 导出雷达设备管理列表
     */
    @ApiOperation(value="导出雷达设备管理列表")
    @PreAuthorize("@ss.hasPermi('system:device:export')")
    @Log(title = "雷达设备管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TRadarDevice tRadarDevice)
    {
        List<TRadarDevice> list = tRadarDeviceService.selectTRadarDeviceList(tRadarDevice);
        ExcelUtil<TRadarDevice> util = new ExcelUtil<TRadarDevice>(TRadarDevice.class);
        util.exportExcel(response, list, "雷达设备管理数据");
    }

    /**
     * 获取雷达设备管理详细信息
     */
    @ApiOperation(value="获取雷达设备管理详细信息")
    @PreAuthorize("@ss.hasPermi('system:device:query')")
    @GetMapping(value = "/{tRadarDeviceId}")
    public AjaxResult getInfo(@PathVariable("tRadarDeviceId") Long tRadarDeviceId)
    {
        return success(tRadarDeviceService.selectTRadarDeviceByTRadarDeviceId(tRadarDeviceId));
    }

    /**
     * 新增雷达设备管理
     */
    @ApiOperation(value="新增雷达设备管理")
    @PreAuthorize("@ss.hasPermi('system:device:add')")
    @Log(title = "雷达设备管理", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResult add(@RequestBody TRadarDevice tRadarDevice)
    {
        return toAjax(tRadarDeviceService.insertTRadarDevice(tRadarDevice));
    }

    /**
     * 修改雷达设备管理
     */
    @ApiOperation(value="修改雷达设备管理")
    @PreAuthorize("@ss.hasPermi('system:device:edit')")
    @Log(title = "雷达设备管理", businessType = BusinessType.UPDATE)
    @PutMapping("edit")
    public AjaxResult edit(@RequestBody TRadarDevice tRadarDevice)
    {
        return toAjax(tRadarDeviceService.updateTRadarDevice(tRadarDevice));
    }

    /**
     * 删除雷达设备管理
     */
    @ApiOperation(value="删除雷达设备管理")
    @PreAuthorize("@ss.hasPermi('system:device:remove')")
    @Log(title = "雷达设备管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{tRadarDeviceIds}")
    public AjaxResult remove(@PathVariable Long[] tRadarDeviceIds)
    {
        return toAjax(tRadarDeviceService.deleteTRadarDeviceByTRadarDeviceIds(tRadarDeviceIds));
    }
}
