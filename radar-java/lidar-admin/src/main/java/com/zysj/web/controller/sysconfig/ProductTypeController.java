package com.zysj.web.controller.sysconfig;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.sysconfig.ProductType;
import com.zysj.system.service.sysconfig.IProductTypeService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2025-01-31
 */
@RestController
@RequestMapping("/sysconfig/productType")
public class ProductTypeController extends BaseController
{
    @Autowired
    private IProductTypeService productTypeService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:type:list')")
    @GetMapping("/list")
    public TableDataInfo list(ProductType productType)
    {
        startPage();
        List<ProductType> list = productTypeService.getList(productType);
        return getDataTable(list);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:type:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(ProductType productType)
    {

        List<ProductType> list = productTypeService.getList(productType);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:type:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProductType productType)
    {
        List<ProductType> list = productTypeService.getList(productType);
        ExcelUtil<ProductType> util = new ExcelUtil<ProductType>(ProductType.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:type:query')")
    @GetMapping(value = "/{productTypeId}")
    public AjaxResult getInfo(@PathVariable("productTypeId") Long productTypeId)
    {
        return success(productTypeService.getInfo(productTypeId));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:type:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProductType productType)
    {
        return toAjax(productTypeService.insert(productType));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:type:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProductType productType)
    {
        return toAjax(productTypeService.update(productType));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:type:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{productTypeIds}")
    public AjaxResult remove(@PathVariable Long[] productTypeIds)
    {
        return toAjax(productTypeService.deleteByIds(productTypeIds));
    }

    /**
     * 产品类型推送
     */
    @PreAuthorize("@ss.hasPermi('system:type:push')")
    @Log(title = "产品类型推送", businessType = BusinessType.PUSH)
    @PostMapping("/batchPushProductTypes")
    public AjaxResult batchPushScanSchemes(@RequestBody List<Long> productTypeIds) throws Exception {
        return toAjax(productTypeService.batchPushProductTypes(productTypeIds));
    }
}
