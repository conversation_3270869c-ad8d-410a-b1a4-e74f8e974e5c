package com.zysj.web.controller.device;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.device.CaptureCard;
import com.zysj.system.service.device.ICaptureCardService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 采集卡管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-26
 */
@RestController
@RequestMapping("/device/captureCard")
public class CaptureCardController extends BaseController
{
    @Autowired
    private ICaptureCardService captureCardService;

    /**
     * 查询采集卡管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:captureCard:list')")
    @GetMapping("/list")
    public TableDataInfo list(CaptureCard captureCard)
    {
        startPage();
        List<CaptureCard> list = captureCardService.getList(captureCard);
        return getDataTable(list);
    }


    /**
     * 查询采集卡管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:captureCard:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(CaptureCard captureCard)
    {
        List<CaptureCard> list = captureCardService.getList(captureCard);
        return getDataTable(list);
    }

    /**
     * 导出采集卡管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:captureCard:export')")
    @Log(title = "采集卡管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CaptureCard captureCard)
    {
        List<CaptureCard> list = captureCardService.getList(captureCard);
        ExcelUtil<CaptureCard> util = new ExcelUtil<CaptureCard>(CaptureCard.class);
        util.exportExcel(response, list, "采集卡管理数据");
    }

    /**
     * 获取采集卡管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:captureCard:query')")
    @GetMapping(value = "/{captureCardId}")
    public AjaxResult getInfo(@PathVariable("captureCardId") Long captureCardId)
    {
        return success(captureCardService.getInfo(captureCardId));
    }

    /**
     * 新增采集卡管理
     */
    @PreAuthorize("@ss.hasPermi('system:captureCard:add')")
    @Log(title = "采集卡管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CaptureCard captureCard)
    {
        return toAjax(captureCardService.insert(captureCard));
    }

    /**
     * 修改采集卡管理
     */
    @PreAuthorize("@ss.hasPermi('system:captureCard:edit')")
    @Log(title = "采集卡管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CaptureCard captureCard)
    {
        return toAjax(captureCardService.update(captureCard));
    }

    /**
     * 删除采集卡管理
     */
    @PreAuthorize("@ss.hasPermi('system:captureCard:remove')")
    @Log(title = "采集卡管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{captureCardIds}")
    public AjaxResult remove(@PathVariable Long[] captureCardIds)
    {
        return toAjax(captureCardService.deleteByIds(captureCardIds));
    }

    /**
     * 打开
     */
    @PreAuthorize("@ss.hasPermi('system:sensor:update')")
    @Log(title = "连接串口", businessType = BusinessType.INSERT)
    @PostMapping("/serialPortOpen")
    public AjaxResult serialPortOpen()
    {
        return toAjax(captureCardService.serialPortOpen());
    }
    /**
     * 打开
     */
    @PreAuthorize("@ss.hasPermi('system:sensor:update')")
    @Log(title = "关闭串口", businessType = BusinessType.INSERT)
    @PostMapping("/serialPortClose")
    public AjaxResult serialPortClose()
    {
        return toAjax(captureCardService.serialPortClose());
    }
}
