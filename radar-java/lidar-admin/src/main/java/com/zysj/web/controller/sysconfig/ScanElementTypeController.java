package com.zysj.web.controller.sysconfig;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.sysconfig.ScanElementType;
import com.zysj.system.service.sysconfig.IScanElementTypeService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2025-02-03
 */
@RestController
@RequestMapping("/sysconfig/scanElementType")
public class ScanElementTypeController extends BaseController
{
    @Autowired
    private IScanElementTypeService scanElementTypeService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:scanElementType:list')")
    @GetMapping("/list")
    public TableDataInfo list(ScanElementType scanElementType)
    {
        startPage();
        List<ScanElementType> list = scanElementTypeService.getList(scanElementType);
        return getDataTable(list);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:scanElementType:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(ScanElementType scanElementType)
    {

        List<ScanElementType> list = scanElementTypeService.getList(scanElementType);
        return getDataTable(list);
    }


    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:scanElementType:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScanElementType scanElementType)
    {
        List<ScanElementType> list = scanElementTypeService.getList(scanElementType);
        ExcelUtil<ScanElementType> util = new ExcelUtil<ScanElementType>(ScanElementType.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:scanElementType:query')")
    @GetMapping(value = "/{scanElementTypeId}")
    public AjaxResult getInfo(@PathVariable("scanElementTypeId") Long scanElementTypeId)
    {
        return success(scanElementTypeService.getInfo(scanElementTypeId));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:scanElementType:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ScanElementType scanElementType)
    {
        return toAjax(scanElementTypeService.insert(scanElementType));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:scanElementType:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ScanElementType scanElementType)
    {
        return toAjax(scanElementTypeService.update(scanElementType));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:scanElementType:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{scanElementTypeIds}")
    public AjaxResult remove(@PathVariable Long[] scanElementTypeIds)
    {
        return toAjax(scanElementTypeService.deleteByIds(scanElementTypeIds));
    }

    /**
     * 监测要素推送
     */
    @PreAuthorize("@ss.hasPermi('system:scanElementType:push')")
    @Log(title = "监测要素推送", businessType = BusinessType.PUSH)
    @PostMapping("/batchPushMoments")
    public AjaxResult batchPushMoments(@RequestBody List<Long> scanElementTypeIds) throws Exception{
        return toAjax(scanElementTypeService.batchPushMoments(scanElementTypeIds));
    }
}
