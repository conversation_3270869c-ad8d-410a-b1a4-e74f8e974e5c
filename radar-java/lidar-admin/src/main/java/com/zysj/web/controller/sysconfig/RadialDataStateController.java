package com.zysj.web.controller.sysconfig;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.sysconfig.RadialDataState;
import com.zysj.system.service.sysconfig.IRadialDataStateService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2025-02-03
 */
@RestController
@RequestMapping("/sysconfig/radialDataState")
public class RadialDataStateController extends BaseController
{
    @Autowired
    private IRadialDataStateService radialDataStateService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:radialDataState:list')")
    @GetMapping("/list")
    public TableDataInfo list(RadialDataState radialDataState)
    {
        startPage();
        List<RadialDataState> list = radialDataStateService.getList(radialDataState);
        return getDataTable(list);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:radialDataState:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(RadialDataState radialDataState)
    {

        List<RadialDataState> list = radialDataStateService.getList(radialDataState);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:radialDataState:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RadialDataState radialDataState)
    {
        List<RadialDataState> list = radialDataStateService.getList(radialDataState);
        ExcelUtil<RadialDataState> util = new ExcelUtil<RadialDataState>(RadialDataState.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:radialDataState:query')")
    @GetMapping(value = "/{radialDataStateId}")
    public AjaxResult getInfo(@PathVariable("radialDataStateId") Long radialDataStateId)
    {
        return success(radialDataStateService.getInfo(radialDataStateId));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:radialDataState:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RadialDataState radialDataState)
    {
        return toAjax(radialDataStateService.insert(radialDataState));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:radialDataState:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RadialDataState radialDataState)
    {
        return toAjax(radialDataStateService.update(radialDataState));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:radialDataState:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{radialDataStateIds}")
    public AjaxResult remove(@PathVariable Long[] radialDataStateIds)
    {
        return toAjax(radialDataStateService.deleteByIds(radialDataStateIds));
    }
}
