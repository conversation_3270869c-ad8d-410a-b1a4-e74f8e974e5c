package com.zysj.web.controller.system;

import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.system.domain.ValueInfoDes;
import com.zysj.system.service.ValueService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;


/**
 * 站点管理Controller
 *
 * <AUTHOR>
 * @date 2024-10-17
 */
@Api(tags = "站点管理")
@RestController
@RequestMapping("/system/value")
public class ValueController extends BaseController {
    @Resource
    private ValueService valueService;

    /**
     * 查询站点管理列表
     */
    @ApiOperation(value = "查询站点管理列表")
    @PreAuthorize("@ss.hasPermi('system:site:list')")
    @GetMapping("/list/{dateStr}")
    public AjaxResult list(@PathVariable("dateStr") String dateStr) {
        Map<Integer, List<ValueInfoDes>> valueInfo = valueService.getValueInfo(dateStr);

        String filePath = "I:\\工作内容\\2025-01\\map_data.xlsx"; // 替换为你的txt文件路径
        // 创建一个新的工作簿
        Workbook workbook = new XSSFWorkbook();
        // 创建一个新的表单
        Sheet sheet = workbook.createSheet("Sheet1");

        Map<Integer, List<ValueInfoDes>> sortedMap = new TreeMap<>(valueInfo);

        // 遍历Map并写入数据到Excel
        int rowNum = 0;
        for (Map.Entry<Integer, List<ValueInfoDes>> entry : sortedMap.entrySet()) {
            Row row = sheet.createRow(rowNum++);
            Cell keyCell1 = row.createCell(0);
            keyCell1.setCellValue("高度/"+entry.getKey()+"米"); // 写入Map的键
            Cell keyCell2 = row.createCell(1);
            keyCell2.setCellValue("时间/10分钟" ); // 写入Map的键
            Cell keyCell4 = row.createCell(3);
            keyCell4.setCellValue("风塔风速"); // 写入Map的键
            Cell keyCell5 = row.createCell(4);
            keyCell5.setCellValue("雷达风速"); // 写入Map的键
            Cell keyCell6 = row.createCell(5);
            keyCell6.setCellValue("风塔风向"); //
            Cell keyCell7 = row.createCell(6);
            keyCell7.setCellValue("雷达风向"); // 写入Map的键

            int colNum = 7;
            for (ValueInfoDes valueInfoDes : entry.getValue()) {
                Row valueRow = sheet.createRow(rowNum++);
                valueRow.createCell(0).setCellValue("");
                valueRow.createCell(1).setCellValue( valueInfoDes.getTimeValue());
                valueRow.createCell(3).setCellValue( valueInfoDes.getAVn());
                valueRow.createCell(4).setCellValue( valueInfoDes.getTjAVn());
                valueRow.createCell(5).setCellValue( valueInfoDes.getADeg());
                valueRow.createCell(6).setCellValue(valueInfoDes.getTjADeg());
            }
        }
        // 自动调整列宽（可选）
        for (int i = 0; i <= 6; i++) { // 根据你的实际数据列数调整
            sheet.autoSizeColumn(i);
        }
        // 将工作簿写入文件
        try (FileOutputStream fileOut = new FileOutputStream(filePath)) {
            workbook.write(fileOut);
        } catch (IOException e) {
            e.printStackTrace();
        }
        // 关闭工作簿
        try {
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }


        return AjaxResult.success(valueInfo);
    }


}
