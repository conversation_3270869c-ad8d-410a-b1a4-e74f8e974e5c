package com.zysj.web.controller.data;

import com.zysj.common.core.domain.AjaxResult;
import com.zysj.system.service.data.IFreqSpecService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.zysj.common.core.domain.AjaxResult.success;

//频谱数据
@RestController
@RequestMapping("/data/freqSpec")
public class FreqSpecController {

    @Resource
    private IFreqSpecService freqSpecService;
    /**
     * 获取状态
     */
    @PreAuthorize("@ss.hasPermi('system:turntable:query')")
    @GetMapping(value = "/getMonitorStatus")
    public AjaxResult getMonitorStatus()
    {
        return success(freqSpecService.getInfo());
    }

}
