package com.zysj.web.controller.device;

import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;
import com.zysj.system.domain.device.LaserParam;
import com.zysj.system.service.device.ILaserService;
import com.zysj.system.service.device.IRadarSysService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 激光Controller
 *
 * <AUTHOR>
 * @date 2025-01-26
 */
@RestController
@RequestMapping("/device/radarSys")
public class RadarSysController extends BaseController {


    @Resource
    private IRadarSysService radarSysService;

    @PreAuthorize("@ss.hasPermi('device:radarSys:query')")
    @GetMapping(value = "/getSysInfo")
    public AjaxResult getSysInfo()
    {
        return success(radarSysService.getSysInfo());
    }

    @PreAuthorize("@ss.hasPermi('device:radarSys:query')")
    @GetMapping(value = "/getNetworkList")
    public AjaxResult getNetworkList()
    {
        return success(radarSysService.getNetworkList());
    }



}
