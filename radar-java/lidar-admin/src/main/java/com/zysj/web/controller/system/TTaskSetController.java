package com.zysj.web.controller.system;

import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.core.page.TableDataInfo;
import com.zysj.common.enums.BusinessType;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.system.domain.TTaskSet;
import com.zysj.system.service.ITTaskSetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 任务参数配置Controller
 *
 * <AUTHOR>
 * @date 2025-01-11
 */
@Api(tags="任务参数配置")
@RestController
@RequestMapping("/system/taskSet")
public class TTaskSetController extends BaseController
{
    @Autowired
    private ITTaskSetService tSetService;

    /**
     * 查询任务参数配置列表
     */
    @ApiOperation(value="查询任务参数配置列表")
    @PreAuthorize("@ss.hasPermi('system:taskSet:list')")
    @GetMapping("/list")
    public TableDataInfo list(TTaskSet tSet)
    {
        startPage();
        List<TTaskSet> list = tSetService.selectTTaskSetList(tSet);
        return getDataTable(list);
    }

    /**
     * 导出任务参数配置列表
     */
    @ApiOperation(value="导出任务参数配置列表")
    @PreAuthorize("@ss.hasPermi('system:taskSet:export')")
    @Log(title = "任务参数配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TTaskSet tSet)
    {
        List<TTaskSet> list = tSetService.selectTTaskSetList(tSet);
        ExcelUtil<TTaskSet> util = new ExcelUtil<TTaskSet>(TTaskSet.class);
        util.exportExcel(response, list, "任务参数配置数据");
    }

    /**
     * 获取任务参数配置详细信息
     */
    @ApiOperation(value="获取任务参数配置详细信息")
    @PreAuthorize("@ss.hasPermi('system:taskSet:query')")
    @GetMapping(value = "/{tSetId}")
    public AjaxResult getInfo(@PathVariable("tSetId") Long tSetId)
    {
        return success(tSetService.selectTTaskSetByTTaskSetId(tSetId));
    }

    /**
     * 新增任务参数配置
     */
    @ApiOperation(value="新增任务参数配置")
    @PreAuthorize("@ss.hasPermi('system:taskSet:add')")
    @Log(title = "任务参数配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TTaskSet tSet)
    {
        return toAjax(tSetService.insertTTaskSet(tSet));
    }

    /**
     * 修改任务参数配置
     */
    @ApiOperation(value="修改任务参数配置")
    @PreAuthorize("@ss.hasPermi('system:taskSet:edit')")
    @Log(title = "任务参数配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TTaskSet tSet)
    {
        return toAjax(tSetService.updateTTaskSet(tSet));
    }

    /**
     * 删除任务参数配置
     */
    @ApiOperation(value="删除任务参数配置")
    @PreAuthorize("@ss.hasPermi('system:taskSet:remove')")
    @Log(title = "任务参数配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{tSetIds}")
    public AjaxResult remove(@PathVariable Long[] tSetIds)
    {
        return toAjax(tSetService.deleteTTaskSetByTTaskSetIds(tSetIds));
    }
}
