package com.zysj.web.controller.device;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.device.ThSensor;
import com.zysj.system.service.device.IThSensorService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 传感器管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-26
 */
@RestController
@RequestMapping("/device/thSensor")
public class ThSensorController extends BaseController
{
    @Autowired
    private IThSensorService thSensorService;

    /**
     * 查询传感器管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:sensor:list')")
    @GetMapping("/list")
    public TableDataInfo list(ThSensor thSensor)
    {
        startPage();
        List<ThSensor> list = thSensorService.getList(thSensor);
        return getDataTable(list);
    }

    /**
     * 查询传感器管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:sensor:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(ThSensor thSensor)
    {
        List<ThSensor> list = thSensorService.getList(thSensor);
        return getDataTable(list);
    }

    /**
     * 导出传感器管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:sensor:export')")
    @Log(title = "传感器管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ThSensor thSensor)
    {
        List<ThSensor> list = thSensorService.getList(thSensor);
        ExcelUtil<ThSensor> util = new ExcelUtil<ThSensor>(ThSensor.class);
        util.exportExcel(response, list, "传感器管理数据");
    }

    /**
     * 获取传感器管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:sensor:query')")
    @GetMapping(value = "/{tSensorDeviceId}")
    public AjaxResult getInfo(@PathVariable("tSensorDeviceId") Long tSensorDeviceId)
    {
        return success(thSensorService.getInfo(tSensorDeviceId));
    }

    /**
     * 获取传感器监控数据
     */
    @PreAuthorize("@ss.hasPermi('system:sensor:query')")
    @GetMapping(value = "/getMonitorStatus")
    public AjaxResult getMonitorStatus()
    {
        return success(thSensorService.getMonitorStatus());
    }

    /**
     * 新增传感器管理
     */
    @PreAuthorize("@ss.hasPermi('system:sensor:add')")
    @Log(title = "传感器管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ThSensor thSensor)
    {
        return toAjax(thSensorService.insert(thSensor));
    }

    /**
     * 修改传感器管理
     */
    @PreAuthorize("@ss.hasPermi('system:sensor:edit')")
    @Log(title = "传感器管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ThSensor thSensor)
    {
        return toAjax(thSensorService.update(thSensor));
    }

    /**
     * 删除传感器管理
     */
    @PreAuthorize("@ss.hasPermi('system:sensor:remove')")
    @Log(title = "传感器管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{tSensorDeviceIds}")
    public AjaxResult remove(@PathVariable Long[] tSensorDeviceIds)
    {
        return toAjax(thSensorService.deleteByIds(tSensorDeviceIds));
    }

    /**
     * 打开
     */
    @PreAuthorize("@ss.hasPermi('system:sensor:update')")
    @Log(title = "连接串口", businessType = BusinessType.INSERT)
    @PostMapping("/serialPortOpen")
    public AjaxResult serialPortOpen()
    {
        return toAjax(thSensorService.serialPortOpen());
    }

    /**
     * 关闭
     */
    @PreAuthorize("@ss.hasPermi('system:sensor:update')")
    @Log(title = "关闭串口", businessType = BusinessType.INSERT)
    @PostMapping("/serialPortClose")
    public AjaxResult serialPortClose()
    {

        return toAjax(thSensorService.serialPortClose());
    }

}
