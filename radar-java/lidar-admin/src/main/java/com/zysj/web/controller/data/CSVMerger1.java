package com.zysj.web.controller.data;

import com.zysj.common.utils.DateUtils;
import com.zysj.system.service.impl.ValueServiceImpl;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.CSVRecord;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

public class CSVMerger1 {


    public static void main(String[] args) throws IOException {
        LocalDate startDate = LocalDate.of(2024, 12, 28);
        String filePathDir = "I:\\工作内容\\2025-03\\LIN\\铁塔数据\\Z_SAND_TOW_C5_54517_";
        String mergedCsvFile = "I:\\工作内容\\2025-03\\LIN\\气象数据文件合并.csv";
        List<Map<String, String>> valueInfoList = new ArrayList<>();
        for (int i = 1; i < 100; i++) {
            startDate = startDate.plusDays(1);
            String dateStr = DateTimeFormatter.ofPattern("yyyyMMdd").format(startDate);
            String filePath = filePathDir + dateStr + "000000.txt";
            valueInfoList.addAll(getValueInfo(filePath, DateTimeFormatter.ofPattern("yyyy-MM-dd").format(startDate)));
        }

        // 合并CSV文件并输出结果
        mergeCSVFiles(valueInfoList, mergedCsvFile);
    }

    /**
     * 读取CSV文件并将每行记录存储为Map<String, String>的列表
     *
     * @param files CSV文件路径列表
     * @return 包含CSV记录的列表，每个记录是一个Map<String, String>
     * @throws IOException 如果发生I/O错误
     */
    private static List<Map<String, String>> readCSVFiles(List<Path> files) {
        List<Map<String, String>> records = new ArrayList<>();
        for (Path file : files) {
            try (BufferedReader reader = Files.newBufferedReader(file);
                 CSVParser csvParser = new CSVParser(reader, CSVFormat.EXCEL.withFirstRecordAsHeader())) {
                for (CSVRecord csvRecord : csvParser) {
                    Map<String, String> record = new HashMap<>();
                    for (int i = 0; i < 51; i++) {
                        String key = csvRecord.getParser().getHeaderNames().get(i).replaceAll("[^a-zA-Z0-9]", "");
                        record.put(key, csvRecord.get(i));
                    }
                    records.add(record);
                }
            } catch (Exception e) {
            }
        }
        return records;
    }

    /**
     * 合并风速和风向的CSV文件，并对风向文件中的数据减去20
     *
     * @throws IOException 如果发生I/O错误
     */
    private static void mergeCSVFiles(List<Map<String, String>> valueInfoList,
                                      String outputFileName) throws IOException {
        // 使用Map来根据'DateTime'组织数据
        Map<String, Map<String, String>> mergedData = new HashMap<>();

        // 添加风塔风速记录
        for (Map<String, String> record : valueInfoList) {
            String dateTime = record.get("DateTime");
            Date date = DateUtils.parseDate(dateTime);
            dateTime = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM, date);
            mergedData.putIfAbsent(dateTime, new HashMap<>());
            for (Map.Entry<String, String> entry : record.entrySet()) {
                if (!"DateTime".equals(entry.getKey())) {
                    String s = entry.getKey().substring(3);
                    if (entry.getKey().substring(0, 3).equals("AVn")) {
                        mergedData.get(dateTime).put(entry.getKey().substring(3) + "m-AVn", entry.getValue());
                    } else if (entry.getKey().substring(0, 4).equals("ADeg")) {
                        mergedData.get(dateTime).put(entry.getKey().substring(4) + "m-Adeg", entry.getValue());
                    }
                }
            }
        }
        // 写入合并后的CSV文件
        Writer writer = new FileWriter(outputFileName);
        List<Integer> hList = new ArrayList<>();
        List<String> headerList = new ArrayList<>();
        headerList.add("DateTime");
        int currentNumber = 80;
        while (currentNumber <= 260) {
            hList.add(currentNumber);
            currentNumber += 20;
        }
        BufferedWriter bufferedWriter = new BufferedWriter(writer);
        try (CSVPrinter csvPrinter = new CSVPrinter(bufferedWriter, CSVFormat.DEFAULT.withHeader())) {
            for (int i = 0; i < hList.size(); i++) {
                headerList.add(hList.get(i) + "m-AVn");
                headerList.add(hList.get(i) + "m-Adeg");
            }
            csvPrinter.printRecord(headerList);

            Set<String> keys = mergedData.keySet();
            List<String> sortedKeys = keys.stream()
                    .sorted() // 按键的自然顺序排序
                    .collect(Collectors.toList());

            // 输出排序后的键
            for (String key : sortedKeys) {
                Map<String, String> dateTimeData = mergedData.get(key);
                List<String> values = new ArrayList<>();
                values.add(dateTimeData.getOrDefault("DateTime", key)); // 这里实际上不需要，因为key一定存在
                for (int i = 0; i < hList.size(); i++) {
                    values.add(dateTimeData.getOrDefault(hList.get(i) + "m-AVn", ""));
                    values.add(dateTimeData.getOrDefault(hList.get(i) + "m-Adeg", ""));
                }
                csvPrinter.printRecord(values);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    public static List<Map<String, String>> getValueInfo(String filePath, String dateStr) {
        try {
            List<Map<String, String>> mapList = new ArrayList<>();
            BufferedReader bufferedReader = null;
            //拆分行列获取数据
            List<String> lines = new ArrayList<>();
            List<String> linesValue = new ArrayList<>();
            // 使用一个Map来根据高度归类数据
            Map<Integer, List<String>> hData = new HashMap<>();
            try {
                bufferedReader = new BufferedReader(new FileReader(filePath));
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    lines.add(line);
                }
                for (int j = 0; j < lines.size(); j++) {
                    String lineStr = lines.get(j).replace(" ", "");
                    //锁定表头
                    if (lineStr.equals("RTH(m)AVnADegARhATem")) {
                        //判断是否整10分钟
                        String timestamp = lines.get(j - 1).replace(" ", "");
                        LocalTime currentTime = LocalTime.parse(timestamp.split("--")[1]);
                        String timeStr = dateStr + " " + currentTime;
                        // 时间加八小时
                        LocalDateTime localDateTime = ValueServiceImpl.getAdd8Time(timeStr);
                        timeStr = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm").format(localDateTime);
                        int minuteOfHour = localDateTime.getMinute();
                        boolean isOnTenMinuteMark = (minuteOfHour % 10 == 0);
                        if (!isOnTenMinuteMark) {
                            linesValue.addAll(lines.subList(j + 1, j + 16));
                            j = j + 16;
                        } else {
                            linesValue.addAll(lines.subList(j + 1, j + 16));
                            j = j + 16;
                            for (int i = 0; i < linesValue.size(); i++) {
                                String[] columns = linesValue.get(i).split("\\s+");
                                if (Integer.valueOf(columns[2]) >= 80 && Integer.valueOf(columns[2]) <= 260) {
                                    int key = Integer.parseInt(columns[2]);
                                    if (key == 250){
                                        key = 260;
                                    }
                                    // 创建高度分类 40 60 80 100 等 以高度为类型 进行归类 进行矢量平均计算
                                    if (!hData.containsKey(key)) {
                                        hData.put(key, new ArrayList<>());
                                    }
                                    hData.get(key).add(linesValue.get(i));
                                }
                            }
                            linesValue = new ArrayList<>();
                            Map<String, String> map = new HashMap<>();
                            map.put("DateTime", timeStr);
                            for (Map.Entry<Integer, List<String>> entry : hData.entrySet()) {
                                List<Integer> windDirections = new ArrayList<>();
                                List<Double> windSpeeds = new ArrayList<>();
                                for (String s : entry.getValue()) {
                                    String[] columns = s.split("\\s+");
                                    windSpeeds.add(Double.valueOf(columns[3]));
                                    windDirections.add(Integer.valueOf(columns[4]));
                                }
                                double averageWindSpeed = calculateAverageWindSpeed(windDirections, windSpeeds);
                                double averageWindDirection = calculateAverageWindDirection(windDirections, windSpeeds);
                                map.put("AVn" + entry.getKey(), String.valueOf(averageWindSpeed));
                                map.put("ADeg" + entry.getKey(), String.valueOf(averageWindDirection));
                            }
                            hData = new HashMap<>();
                            mapList.add(map);
                        }
                    }
                }
                return mapList;
            } catch (IOException e) {
            } finally {
                try {
                    if (bufferedReader != null) {
                        bufferedReader.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            return new ArrayList<>();
        } catch (Exception e) {
        }
        return new ArrayList<>();
    }


    /**
     * 计算平均风速
     *
     * @param windDirections 风向数组（角度，单位：度）
     * @param windSpeeds     风速数组（单位：米/秒）
     * @return 平均风速（单位：米/秒）
     * ‌变量定义‌：
     * <p>
     * windDirections：风向数组，存储每个观测点的风向（角度，单位：度）。
     * windSpeeds：风速数组，存储每个观测点的风速（单位：米/秒）。
     * ‌函数calculateAverageWindSpeed‌：
     * <p>
     * 参数：windDirections和windSpeeds，分别表示风向和风速数组。
     * 检查数组长度是否一致，如果不一致则抛出异常。
     * 初始化sumVx和sumVy为0.0，用于存储x和y方向分量的总和。
     * 遍历每个观测点，将风向从角度转换为弧度，并计算x和y方向的分量。
     * 将每个观测点的x和y方向分量累加到sumVx和sumVy中。
     * 计算x和y方向分量的平均值avgVx和avgVy。
     * 使用Math.sqrt计算平均风速，即sqrt(avgVx^2 + avgVy^2)。
     */
    public static double calculateAverageWindSpeed(List<Integer> windDirections, List<Double> windSpeeds) {
        // 检查数组长度是否一致
        if (windDirections.size() != windSpeeds.size()) {
            throw new IllegalArgumentException("风向和风速数组长度不一致");
        }

        double sumVx = 0.0;
        double sumVy = 0.0;

        // 计算x和y方向分量的总和
        for (int i = 0; i < windDirections.size(); i++) {
            double windDirectionRadians = Math.toRadians(windDirections.get(i));
            double vx = windSpeeds.get(i) * Math.cos(windDirectionRadians);
            double vy = windSpeeds.get(i) * Math.sin(windDirectionRadians);

            sumVx += vx;
            sumVy += vy;
        }

        // 计算x和y方向分量的平均值
        double avgVx = sumVx / windDirections.size();
        double avgVy = sumVy / windDirections.size();

        // 合成平均风速
        double averageWindSpeed = Math.sqrt(avgVx * avgVx + avgVy * avgVy);

        return averageWindSpeed;
    }

    /**
     * 计算平均风向（矢量平均法）
     *
     * @param windDirections 风向数组（角度）
     * @param windSpeeds     风速数组（对应风向的风速）
     * @return 平均风向（角度）
     * ‌变量定义‌：
     * <p>
     * windDirections：风向数组，存储每个观测点的风向（角度）。
     * windSpeeds：风速数组，存储与风向对应的风速（米/秒）。
     * ‌计算平均风向‌：
     * <p>
     * calculateAverageWindDirection方法使用矢量平均法计算平均风向。
     * 遍历风向和风速数组，将每个风向转换为弧度，并计算x和y方向的分量（使用风速作为权重）。
     * 使用Math.atan2方法计算平均风向的弧度值。
     * 将弧度转换回角度，并调整角度到0-360度范围内
     */
    public static double calculateAverageWindDirection(List<Integer> windDirections, List<Double> windSpeeds) {
        double sumSin = 0.0;
        double sumCos = 0.0;

        // 遍历风向和风速数组，计算x和y方向的分量
        for (int i = 0; i < windDirections.size(); i++) {
            int direction = windDirections.get(i);
            double speed = windSpeeds.get(i);

            // 将角度转换为弧度
            double radians = Math.toRadians(direction);

            // 计算x和y方向的分量
            sumSin += speed * Math.sin(radians);
            sumCos += speed * Math.cos(radians);
        }

        // 计算平均风向的弧度值
        double averageRadians = Math.atan2(sumSin, sumCos);

        // 将弧度转换回角度
        double averageDirection = Math.toDegrees(averageRadians);

        // 调整角度到0-360度范围内
        if (averageDirection < 0) {
            averageDirection += 360.0;
        }

        return averageDirection;
    }

    /**
     * 计算样本的方差
     *
     * @param data 样本数据
     * @return 方差
     */
    public static double variance(double[] data) {
        double mean = mean(data);
        double sumSquaredDeviations = 0;
        for (double datum : data) {
            sumSquaredDeviations += Math.pow(datum - mean, 2);
        }
        return sumSquaredDeviations / (data.length - 1);
    }

    /**
     * 计算样本的标准差
     *
     * @param data 样本数据
     * @return 标准差
     */
    public static double stdDev(double[] data) {
        return Math.sqrt(variance(data));
    }

    /**
     * 计算样本的平均值
     *
     * @param data 样本数据
     * @return 平均值
     */
    private static double mean(double[] data) {
        double sum = 0;
        for (double datum : data) {
            sum += datum;
        }
        return sum / data.length;
    }
//    public static void main(String[] args) {
//        double[] data = {1.0, 2.0, 3.0, 4.0, 5.0};
//        System.out.println("方差: " + variance(data));
//        System.out.println("标准差: " + stdDev(data));
//    }

    public class SimpleLinearRegression {
        private double a; // 斜率
        private double b; // 截距

        public SimpleLinearRegression(double[] x, double[] y) {
            if (x.length != y.length) {
                throw new IllegalArgumentException("x和y数组长度不匹配");
            }
            double sumX = 0.0;
            double sumY = 0.0;
            double sumXY = 0.0;
            double sumXX = 0.0;
            int n = x.length;
            for (int i = 0; i < n; i++) {
                sumX += x[i];
                sumY += y[i];
                sumXY += x[i] * y[i];
                sumXX += x[i] * x[i];
            }
            a = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
            b = (sumY - a * sumX) / n;
        }

        public double getSlope() {
            return a;
        }

        public double getIntercept() {
            return b;
        }
//        public static void main(String[] args) {
//            double[] x = {1.0, 2.0, 3.0, 4.0, 5.0};
//            double[] y = {2.0, 4.0, 6.0, 8.0, 10.0};
//            SimpleLinearRegression regression = new SimpleLinearRegression(x, y);
//            System.out.println("斜率 a: " + regression.getSlope());
//            System.out.println("截距 b: " + regression.getIntercept());
//        }
    }

}
