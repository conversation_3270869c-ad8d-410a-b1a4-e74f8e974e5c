package com.zysj.web.controller.data;

import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.system.domain.dto.DataQueryDTO;
import com.zysj.system.domain.dto.DataQueryResultDTO;
import com.zysj.system.domain.dto.DataQueryWideResultDTO;
import com.zysj.system.domain.enums.PPIDataType;
import com.zysj.system.listener.RedisMessagePublisher;
import com.zysj.system.service.data.IPPIDataService;
import com.zysj.system.service.data.IRadarFramesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 数据存储Controller，用于模拟数据存储请求
 * 
 * <AUTHOR>
 * @date 2025-01-03
 */
@Api(tags = "数据存储管理")
@RestController
@RequestMapping("/data/storage")
public class DataStorageController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(DataStorageController.class);

    @Autowired
    private IRadarFramesService radarFramesService;

    @Autowired
    private IPPIDataService ppiDataService;

    @Autowired
    private RedisMessagePublisher redisMessagePublisher;

    /**
     * 保存径向数据
     */
    @ApiOperation("保存径向数据")
//    @PreAuthorize("@ss.hasPermi('system:storage:save')")
    @PostMapping("/radial")
    public AjaxResult saveRadialData(@RequestBody String jsonData) {
        try {
            redisMessagePublisher.publish("radial_data", jsonData);
            return AjaxResult.success("success");
        } catch (Exception e) {
            log.error("Failed to process radial data", e);
            return AjaxResult.error("Failed to process data");
        }
    }

    /**
     * 保存风廓线数据
     */
    @ApiOperation("保存风廓线数据")
//    @PreAuthorize("@ss.hasPermi('system:storage:save')")
    @PostMapping("/windprofile")
    public AjaxResult saveWindProfileData(@RequestBody String jsonData) {
        try {
            redisMessagePublisher.publish("wind_profile_data", jsonData);
            return AjaxResult.success("success");
        } catch (Exception e) {
            log.error("Failed to process wind profile data", e);
            return AjaxResult.error("Failed to process data");
        }
    }

    /**
     * 查询数据
     */
    @ApiOperation("查询数据")
//    @PreAuthorize("@ss.hasPermi('system:storage:query')")
    @PostMapping("/query")
    public AjaxResult queryData(@RequestBody DataQueryDTO queryDTO) {
        DataQueryResultDTO result = radarFramesService.queryData(queryDTO);
        return AjaxResult.success(result);
    }

    /**
     * 查询基数据
     */
    @ApiOperation("查询基数据")
    @PostMapping("/queryBase")
    public AjaxResult baseQuery(@RequestBody DataQueryDTO queryDTO) {
        DataQueryWideResultDTO result = radarFramesService.queryBaseData(queryDTO);
        return AjaxResult.success(result);
    }

    /**
     * 查询PPI实时数据
     */
    @ApiOperation("查询PPI实时数据")
    @GetMapping("/ppi/realtime")
    public AjaxResult getRealtimePPIData(
            @ApiParam(value = "数据类型", required = false, defaultValue = "VELOCITY") 
            @RequestParam(value = "dataType", required = false, defaultValue = "VELOCITY") String dataType) {
        try {
            PPIDataType type = "SNR".equalsIgnoreCase(dataType) ? PPIDataType.SNR : PPIDataType.VELOCITY;
            return AjaxResult.success(ppiDataService.getRealtimePPIData(type));
        } catch (Exception e) {
            //不返回异常，有前台判断数据是否为空
            log.error("Failed to get realtime PPI data", e);
            return AjaxResult.success();
        }
    }

    /**
     * 查询指定时间范围内的PPI数据
     */
    @ApiOperation("查询历史PPI数据")
    @PostMapping("/ppi/history")
    public AjaxResult getHistoricalPPIData(@RequestBody DataQueryDTO queryDTO) {
        try {
            Date startTime = queryDTO.getStartTime();
            Date endTime = queryDTO.getEndTime();
            return AjaxResult.success(ppiDataService.getHistoricalPPIData(startTime, endTime));
        } catch (Exception e) {
            log.error("Failed to get historical PPI data", e);
            return AjaxResult.error("Failed to get historical PPI data");
        }
    }
} 