package com.zysj.web.controller.data;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.math3.stat.descriptive.moment.Variance;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class WindAnalysis {

    public static void main(String[] args) throws IOException, ParseException {
        String csvFilePath = "path/to/your/input.csv";
        String excelFilePath = "path/to/your/output.xlsx";
        List<CSVRecord> records = readCSV(csvFilePath);
        Map<String, Map<String, Double>> results = processData(records);
        exportToExcel(results, excelFilePath);
    }

    private static List<CSVRecord> readCSV(String csvFilePath) throws IOException {
        FileInputStream fis = new FileInputStream(csvFilePath);
        CSVParser parser = CSVParser.parse(fis, StandardCharsets.UTF_8, CSVFormat.DEFAULT.withFirstRecordAsHeader());
        return parser.getRecords();
    }

    private static Map<String, Map<String, Double>> processData(List<CSVRecord> records) throws ParseException {
        Map<String, Map<String, Double>> results = new LinkedHashMap<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (CSVRecord record : records) {
            String dateTime = record.get("DateTime");
            Map<String, Double> variancesAndStdDevs = new LinkedHashMap<>();
            for (int i = 40; i <= 1020; i += 20) {
                String radarKey = "56-" + i;
                String towerKey = "57-" + i;
                double radarSpeed = Double.parseDouble(record.get(radarKey));
                double towerSpeed = Double.parseDouble(record.get(towerKey));
                Variance variance = new Variance();
                double[] speeds = {radarSpeed, towerSpeed};
                double var = variance.evaluate(speeds);
                double stdDev = Math.sqrt(var);
                variancesAndStdDevs.put(i + "米方差", var);
                variancesAndStdDevs.put(i + "米标准差", stdDev);
            }
            results.put(dateTime, variancesAndStdDevs);
        }

        return results;
    }

    private static void exportToExcel(Map<String, Map<String, Double>> results, String excelFilePath) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Wind Speed Analysis");
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("DateTime");
        int cellIndex = 1;
        for (int i = 40; i <= 1000; i += 20) {
            headerRow.createCell(cellIndex++).setCellValue(i + "米方差");
            headerRow.createCell(cellIndex++).setCellValue(i + "米标准差");
        }
        int rowIndex = 1;
        for (Map.Entry<String, Map<String, Double>> entry : results.entrySet()) {
            Row row = sheet.createRow(rowIndex++);
            row.createCell(0).setCellValue(entry.getKey());
            cellIndex = 1;
            for (int i = 40; i <= 1000; i += 20) {
                row.createCell(cellIndex++).setCellValue(entry.getValue().get(i + "米方差"));
                row.createCell(cellIndex++).setCellValue(entry.getValue().get(i + "米标准差"));
            }
        }
        try (FileOutputStream fileOut = new FileOutputStream(excelFilePath)) {
            workbook.write(fileOut);
        }
        workbook.close();
    }
}