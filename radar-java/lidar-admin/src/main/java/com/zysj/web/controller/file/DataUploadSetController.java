package com.zysj.web.controller.file;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.file.DataUploadSet;
import com.zysj.system.service.file.IDataUploadSetService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 数据上传设置Controller
 * 
 * <AUTHOR>
 * @date 2025-02-03
 */
@RestController
@RequestMapping("/file/dataUploadSet")
public class DataUploadSetController extends BaseController
{
    @Autowired
    private IDataUploadSetService dataUploadSetService;

    /**
     * 查询数据上传设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataUploadSet:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataUploadSet dataUploadSet)
    {
        startPage();
        List<DataUploadSet> list = dataUploadSetService.getList(dataUploadSet);
        return getDataTable(list);
    }

    /**
     * 查询数据上传设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataUploadSet:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(DataUploadSet dataUploadSet)
    {

        List<DataUploadSet> list = dataUploadSetService.getList(dataUploadSet);
        return getDataTable(list);
    }

    /**
     * 导出数据上传设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataUploadSet:export')")
    @Log(title = "数据上传设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataUploadSet dataUploadSet)
    {
        List<DataUploadSet> list = dataUploadSetService.getList(dataUploadSet);
        ExcelUtil<DataUploadSet> util = new ExcelUtil<DataUploadSet>(DataUploadSet.class);
        util.exportExcel(response, list, "数据上传设置数据");
    }

    /**
     * 获取数据上传设置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dataUploadSet:query')")
    @GetMapping(value = "/{dataUploadSetId}")
    public AjaxResult getInfo(@PathVariable("dataUploadSetId") Long dataUploadSetId)
    {
        return success(dataUploadSetService.getInfo(dataUploadSetId));
    }

    /**
     * 新增数据上传设置
     */
    @PreAuthorize("@ss.hasPermi('system:dataUploadSet:add')")
    @Log(title = "数据上传设置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataUploadSet dataUploadSet)
    {
        return toAjax(dataUploadSetService.insert(dataUploadSet));
    }

    /**
     * 修改数据上传设置
     */
    @PreAuthorize("@ss.hasPermi('system:dataUploadSet:edit')")
    @Log(title = "数据上传设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataUploadSet dataUploadSet)
    {
        return toAjax(dataUploadSetService.update(dataUploadSet));
    }

    /**
     * 删除数据上传设置
     */
    @PreAuthorize("@ss.hasPermi('system:dataUploadSet:remove')")
    @Log(title = "数据上传设置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dataUploadSetIds}")
    public AjaxResult remove(@PathVariable Long[] dataUploadSetIds)
    {
        return toAjax(dataUploadSetService.deleteByIds(dataUploadSetIds));
    }
}
