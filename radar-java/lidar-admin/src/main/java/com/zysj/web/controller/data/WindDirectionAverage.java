package com.zysj.web.controller.data;

import java.util.Arrays;
import java.util.List;

public class WindDirectionAverage {

    /**
     * 将风向值从度数转换为弧度
     *
     * @param degrees 风向度数
     * @return 风向弧度
     */
    private static double degreesToRadians(double degrees) {
        return degrees * Math.PI / 180.0;
    }

    /**
     * 计算风向的平均方向（使用矢量平均方法）
     *
     * @param directions 风向度数列表
     * @return 平均风向度数
     */
    public static double calculateAverageDirection(List<Double> directions) {
        double sumX = 0.0;
        double sumY = 0.0;
        int n = directions.size();

        for (double direction : directions) {
            double radians = degreesToRadians(direction);
            sumX += Math.cos(radians);
            sumY += Math.sin(radians);
        }

        double meanX = sumX / n;
        double meanY = sumY / n;
        double averageRadians = Math.atan2(meanY, meanX);

        // 将结果转换回度数
        double averageDegrees = radiansToDegrees(averageRadians);

        // 确保结果在0到359度之间
        if (averageDegrees < 0) {
            averageDegrees += 360.0;
        }

        return averageDegrees;
    }

    /**
     * 将风向值从弧度转换为度数
     *
     * @param radians 风向弧度
     * @return 风向度数
     */
    private static double radiansToDegrees(double radians) {
        return radians * 180.0 / Math.PI;
    }

    public static double calculateAverageWindDirection(List<Integer> windDirections, List<Double> windSpeeds) {
        double sumSin = 0.0;
        double sumCos = 0.0;

        // 遍历风向和风速数组，计算x和y方向的分量
        for (int i = 0; i < windDirections.size(); i++) {
            int direction = windDirections.get(i);
            double speed = windSpeeds.get(i);

            // 将角度转换为弧度
            double radians = Math.toRadians(direction);

            // 计算x和y方向的分量
            sumSin += speed * Math.sin(radians);
            sumCos += speed * Math.cos(radians);
        }

        // 计算平均风向的弧度值
        double averageRadians = Math.atan2(sumSin, sumCos);

        // 将弧度转换回角度
        double averageDirection = Math.toDegrees(averageRadians);

        // 调整角度到0-360度范围内
        if (averageDirection < 0) {
            averageDirection += 360.0;
        }

        return averageDirection;
    }

    public static void main(String[] args) {
        List<Integer> windDirections = Arrays.asList( 210,
                199,
                208,
                203,
                210,
                193,
                203,
                205,
                200,
                213);
        List<Double> windSpeeds = Arrays.asList(2.0,
                2.1,
                2.4,
                2.9,
                2.5,
                2.2,
                1.9,
                1.6,
                1.8,
                1.7 );
        double averageDirection = calculateAverageWindDirection(windDirections,windSpeeds);
        System.out.println("平均风向: " + averageDirection + " 度");
    }
}
