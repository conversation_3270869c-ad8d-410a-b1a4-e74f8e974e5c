package com.zysj.web.controller.scan;

import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.core.page.TableDataInfo;
import com.zysj.common.enums.BusinessType;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.system.domain.scan.ScanScheme;
import com.zysj.system.service.scan.IScanSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 任务参数配置Controller
 *
 * <AUTHOR>
 * @date 2025-01-26
 */
@RestController
@RequestMapping("/scan/scanScheme")
public class ScanSchemeController extends BaseController {
    @Autowired
    private IScanSchemeService scanSchemeService;

    /**
     * 查询任务参数配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:scheme:list')")
    @GetMapping("/list")
    public TableDataInfo list(ScanScheme scanScheme) {
        startPage();
        List<ScanScheme> list = scanSchemeService.getList(scanScheme);
        return getDataTable(list);
    }

    /**
     * 查询任务参数配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:scheme:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(ScanScheme scanScheme) {

        List<ScanScheme> list = scanSchemeService.getList(scanScheme);
        return getDataTable(list);
    }


    /**
     * 导出任务参数配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:scheme:export')")
    @Log(title = "任务参数配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScanScheme scanScheme) {
        List<ScanScheme> list = scanSchemeService.getList(scanScheme);
        ExcelUtil<ScanScheme> util = new ExcelUtil<ScanScheme>(ScanScheme.class);
        util.exportExcel(response, list, "任务参数配置数据");
    }

    /**
     * 获取任务参数配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:scheme:query')")
    @GetMapping(value = "/{scanSchemeId}")
    public AjaxResult getInfo(@PathVariable("scanSchemeId") Long scanSchemeId) {
        return success(scanSchemeService.getInfo(scanSchemeId));
    }


    /**
     * 新增任务参数配置
     */
    @PreAuthorize("@ss.hasPermi('system:scheme:add')")
    @Log(title = "任务参数配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ScanScheme scanScheme) throws Exception {

        return toAjax(scanSchemeService.insert(scanScheme));
    }

    /**
     * 修改任务参数配置
     */
    @PreAuthorize("@ss.hasPermi('system:scheme:edit')")
    @Log(title = "任务参数配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ScanScheme scanScheme) {
        return toAjax(scanSchemeService.update(scanScheme));
    }

    /**
     * 删除任务参数配置
     */
    @PreAuthorize("@ss.hasPermi('system:scheme:remove')")
    @Log(title = "任务参数配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{scanSchemeIds}")
    public AjaxResult remove(@PathVariable Long[] scanSchemeIds) {
        return toAjax(scanSchemeService.deleteByIds(scanSchemeIds));
    }

    /**
     * 更新雷达中的扫描方案
     */
    @PreAuthorize("@ss.hasPermi('system:scheme:dit')")
    @Log(title = "更新雷达中的扫描方案", businessType = BusinessType.UPDATA_DEVICE)
    @PostMapping("/updateSchemeToRadar")
    public AjaxResult updateSchemeToRadar() {

        return toAjax(scanSchemeService.updateSchemeToDevice());
    }

    /**
     * 扫描方案推送
     */
    @PreAuthorize("@ss.hasPermi('system:scheme:push')")
    @Log(title = "扫描方案推送", businessType = BusinessType.PUSH)
    @PostMapping("/batchPushScanSchemes")
    public AjaxResult batchPushScanSchemes(@RequestBody List<Long> scanSchemeIds) throws Exception{
        return toAjax(scanSchemeService.batchPushScanSchemes(scanSchemeIds));
    }

}
