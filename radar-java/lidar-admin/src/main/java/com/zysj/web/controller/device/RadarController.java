package com.zysj.web.controller.device;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.device.Radar;
import com.zysj.system.service.device.IRadarService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;


import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 雷达Controller
 * 
 * <AUTHOR>
 * @date 2025-01-26
 */
@RestController
@RequestMapping("/device/radar")
public class RadarController extends BaseController
{
    @Autowired
    private IRadarService radarService;

    /**
     * 查询雷达列表
     */
    @PreAuthorize("@ss.hasPermi('system:radar:list')")
    @GetMapping("/list")
    public TableDataInfo list(Radar radar)
    {
        startPage();
        List<Radar> list = radarService.getList(radar);
        return getDataTable(list);
    }


    /**
     * 查询雷达列表
     */
    @PreAuthorize("@ss.hasPermi('system:radar:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(Radar radar)
    {
        List<Radar> list = radarService.getList(radar);
        return getDataTable(list);
    }


    /**
     * 导出雷达列表
     */
    @PreAuthorize("@ss.hasPermi('system:radar:export')")
    @Log(title = "雷达", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Radar radar)
    {
        List<Radar> list = radarService.getList(radar);
        ExcelUtil<Radar> util = new ExcelUtil<Radar>(Radar.class);
        util.exportExcel(response, list, "雷达数据");
    }

    /**
     * 获取雷达详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:radar:query')")
    @GetMapping(value = "/{radarId}")
    public AjaxResult getInfo(@PathVariable("radarId") Long radarId)
    {
        return success(radarService.getInfo(radarId));
    }

    /**
     * 新增雷达
     */
    @PreAuthorize("@ss.hasPermi('system:radar:add')")
    @Log(title = "雷达", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Radar radar)
    {
        return toAjax(radarService.insert(radar));
    }

    /**
     * 修改雷达
     */
    @PreAuthorize("@ss.hasPermi('system:radar:edit')")
    @Log(title = "雷达", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Radar radar)
    {
        return toAjax(radarService.update(radar));
    }


    /**
     * 更新数据到工控机
     */
    @PreAuthorize("@ss.hasPermi('system:radar:edit')")
    @Log(title = "雷达", businessType = BusinessType.UPDATE)
    @PutMapping("/pushToDevice")
    public AjaxResult pushToDevice()
    {
        return toAjax(radarService.pushToDevice());
    }



    /**
     * 删除雷达
     */
    @PreAuthorize("@ss.hasPermi('system:radar:remove')")
    @Log(title = "雷达", businessType = BusinessType.DELETE)
	@DeleteMapping("/{radarIds}")
    public AjaxResult remove(@PathVariable Long[] radarIds)
    {
        return toAjax(radarService.deleteByIds(radarIds));
    }

    /**
     * 雷达参数推送
     */
    @PreAuthorize("@ss.hasPermi('system:radar:push')")
    @Log(title = "雷达参数推送", businessType = BusinessType.PUSH)
    @PostMapping("/batchPushRadarConfig")
    public AjaxResult batchPushRadarConfig(@RequestBody Radar radar) throws Exception{
        return toAjax(radarService.batchPushRadarConfig(radar));
    }
}
