package com.zysj.web.controller.device;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.device.TurntableParam;
import com.zysj.system.service.device.ITurntableParamService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 工控机转台参数管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-26
 */
@RestController
@RequestMapping("/device/param")
public class TurntableParamController extends BaseController
{
    @Autowired
    private ITurntableParamService turntableParamService;

    /**
     * 查询工控机转台参数管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:param:list')")
    @GetMapping("/list")
    public TableDataInfo list(TurntableParam turntableParam)
    {
        startPage();
        List<TurntableParam> list = turntableParamService.getList(turntableParam);
        return getDataTable(list);
    }

    /**
     * 查询工控机转台参数管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:param:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(TurntableParam turntableParam)
    {
        List<TurntableParam> list = turntableParamService.getList(turntableParam);
        return getDataTable(list);
    }


    /**
     * 导出工控机转台参数管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:param:export')")
    @Log(title = "工控机转台参数管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TurntableParam turntableParam)
    {
        List<TurntableParam> list = turntableParamService.getList(turntableParam);
        ExcelUtil<TurntableParam> util = new ExcelUtil<TurntableParam>(TurntableParam.class);
        util.exportExcel(response, list, "工控机转台参数管理数据");
    }

    /**
     * 获取工控机转台参数管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:param:query')")
    @GetMapping(value = "/{turntableParamId}")
    public AjaxResult getInfo(@PathVariable("turntableParamId") Long turntableParamId)
    {
        return success(turntableParamService.getInfo(turntableParamId));
    }

    /**
     * 新增工控机转台参数管理
     */
    @PreAuthorize("@ss.hasPermi('system:param:add')")
    @Log(title = "工控机转台参数管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TurntableParam turntableParam)
    {
        return toAjax(turntableParamService.insert(turntableParam));
    }

    /**
     * 修改工控机转台参数管理
     */
    @PreAuthorize("@ss.hasPermi('system:param:edit')")
    @Log(title = "工控机转台参数管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TurntableParam turntableParam)
    {
        return toAjax(turntableParamService.update(turntableParam));
    }

    /**
     * 删除工控机转台参数管理
     */
    @PreAuthorize("@ss.hasPermi('system:param:remove')")
    @Log(title = "工控机转台参数管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{turntableParamIds}")
    public AjaxResult remove(@PathVariable Long[] turntableParamIds)
    {
        return toAjax(turntableParamService.deleteByIds(turntableParamIds));
    }
}
