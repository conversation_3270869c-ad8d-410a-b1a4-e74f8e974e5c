package com.zysj.web.controller.log;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.log.SysMaintenLog;
import com.zysj.system.service.log.ISysMaintenLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2025-02-09
 */
@RestController
@RequestMapping("/log/sysMaintenLog")
public class SysMaintenLogController extends BaseController
{
    @Autowired
    private ISysMaintenLogService sysMaintenLogService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:sysMaintenLog:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysMaintenLog sysMaintenLog)
    {
        startPage();
        List<SysMaintenLog> list = sysMaintenLogService.getList(sysMaintenLog);
        return getDataTable(list);
    }
    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:sysMaintenLog:list')")
    @GetMapping("/listAll")
    public AjaxResult getAllList(SysMaintenLog sysMaintenLog)
    {
        List<SysMaintenLog> list = sysMaintenLogService.getList(sysMaintenLog);
        return success(list);
    }


    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:sysMaintenLog:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysMaintenLog sysMaintenLog)
    {
        List<SysMaintenLog> list = sysMaintenLogService.getList(sysMaintenLog);
        ExcelUtil<SysMaintenLog> util = new ExcelUtil<SysMaintenLog>(SysMaintenLog.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:sysMaintenLog:query')")
    @GetMapping(value = "/{sysMaintenLogId}")
    public AjaxResult getInfo(@PathVariable("sysMaintenLogId") Integer sysMaintenLogId)
    {
        return success(sysMaintenLogService.getInfo(sysMaintenLogId));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:sysMaintenLog:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysMaintenLog sysMaintenLog)
    {
        return toAjax(sysMaintenLogService.insert(sysMaintenLog));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:sysMaintenLog:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysMaintenLog sysMaintenLog)
    {
        return toAjax(sysMaintenLogService.update(sysMaintenLog));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:sysMaintenLog:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{sysMaintenLogIds}")
    public AjaxResult remove(@PathVariable Integer[] sysMaintenLogIds)
    {
        return toAjax(sysMaintenLogService.deleteByIds(sysMaintenLogIds));
    }
}
