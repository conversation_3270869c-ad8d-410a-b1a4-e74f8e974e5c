package com.zysj.web.controller.radarweb;

import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.exception.BusinessException;
import com.zysj.common.exception.ErrorCode;
import com.zysj.system.domain.WindLidarStatus;
import com.zysj.system.domain.req.RadarCommandRequest;
//import com.zysj.system.service.radarweb.RadarService;
import com.zysj.system.service.radarweb.RadarWebService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


@Api(tags="radar")
@RestController
@RequestMapping("/radar")
@Slf4j
//@CrossOrigin(origins = "http://localhost:3000", allowCredentials = "true")
public class RadarWebController {

    @Resource
    private RadarWebService radarService;

    @Resource
    private RedisTemplate redisTemplate;

//    @RequestMapping("/init")
//    public AjaxResult init(@RequestBody RadarCommandRequest radarCommandRequest) {
//        if (radarCommandRequest == null) {
//            throw new BusinessException(ErrorCode.PARAMS_ERROR);
//        }
//        boolean res = radarService.init(radarCommandRequest);
//        if (!res) {
//            return AjaxResult.error(ErrorCode.SYSTEM_ERROR.getMessage());
//        }
//        return AjaxResult.success(res);
//    }
//
//    @RequestMapping("/start")
//    public AjaxResult start(@RequestBody RadarCommandRequest radarCommandRequest) {
//        if (radarCommandRequest == null) {
//            throw new BusinessException(ErrorCode.NULL_ERROR);
//        }
//        System.out.println("controller---------------------------");
//        System.out.println("radarRequest: " + radarCommandRequest);
////        radarCommandRequest.setCommandData("sss");
//        boolean res = radarService.start(radarCommandRequest);
//        return AjaxResult.success(res);
//    }
//
//    @RequestMapping("/getStatus")
//    public AjaxResult getStatus(@RequestBody RadarCommandRequest radarCommandRequest) {
//        if (radarCommandRequest == null) {
//            throw new BusinessException(ErrorCode.PARAMS_ERROR);
//        }
//        WindLidarStatus.WindLidarData windLidarData = radarService.getStatus(radarCommandRequest);
//        if (windLidarData == null) {
//            throw new BusinessException(ErrorCode.NULL_ERROR);
//        }
//        return AjaxResult.success(windLidarData);
//    }
    @RequestMapping("/test")
    public AjaxResult test() {

         String  str= radarService.test();
        return AjaxResult.success(str);

    }

}
