package com.zysj.web.controller.log;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.log.TaskRunLog;
import com.zysj.system.service.log.ITaskRunLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2025-02-09
 */
@RestController
@RequestMapping("/log/taskRunLog")
public class TaskRunLogController extends BaseController
{
    @Autowired
    private ITaskRunLogService taskRunLogService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:taskRunLog:list')")
    @GetMapping("/list")
    public TableDataInfo list(TaskRunLog taskRunLog)
    {
        startPage();
        List<TaskRunLog> list = taskRunLogService.getList(taskRunLog);
        return getDataTable(list);
    }
    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:taskRunLog:list')")
    @GetMapping("/listAll")
    public AjaxResult getAllList(TaskRunLog taskRunLog)
    {
        List<TaskRunLog> list = taskRunLogService.getList(taskRunLog);
        return success(list);
    }


    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:taskRunLog:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TaskRunLog taskRunLog)
    {
        List<TaskRunLog> list = taskRunLogService.getList(taskRunLog);
        ExcelUtil<TaskRunLog> util = new ExcelUtil<TaskRunLog>(TaskRunLog.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:taskRunLog:query')")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId)
    {
        return success(taskRunLogService.getInfo(logId));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:taskRunLog:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TaskRunLog taskRunLog)
    {
        return toAjax(taskRunLogService.insert(taskRunLog));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:taskRunLog:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TaskRunLog taskRunLog)
    {
        return toAjax(taskRunLogService.update(taskRunLog));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:taskRunLog:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds)
    {
        return toAjax(taskRunLogService.deleteByIds(logIds));
    }
}
