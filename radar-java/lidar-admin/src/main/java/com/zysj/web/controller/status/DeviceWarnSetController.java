package com.zysj.web.controller.status;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.status.DeviceWarnSet;
import com.zysj.system.service.status.IDeviceWarnSetService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 设备预警设置Controller
 * 
 * <AUTHOR>
 * @date 2025-02-03
 */
@RestController
@RequestMapping("/status/deviceWarnSet")
public class DeviceWarnSetController extends BaseController
{
    @Autowired
    private IDeviceWarnSetService deviceWarnSetService;

    /**
     * 查询设备预警设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:deviceWarnSet:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeviceWarnSet deviceWarnSet)
    {
        startPage();
        List<DeviceWarnSet> list = deviceWarnSetService.getList(deviceWarnSet);
        return getDataTable(list);
    }

    /**
     * 查询设备预警设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:deviceWarnSet:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(DeviceWarnSet deviceWarnSet)
    {

        List<DeviceWarnSet> list = deviceWarnSetService.getList(deviceWarnSet);
        return getDataTable(list);
    }
    /**
     * 导出设备预警设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:deviceWarnSet:export')")
    @Log(title = "设备预警设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DeviceWarnSet deviceWarnSet)
    {
        List<DeviceWarnSet> list = deviceWarnSetService.getList(deviceWarnSet);
        ExcelUtil<DeviceWarnSet> util = new ExcelUtil<DeviceWarnSet>(DeviceWarnSet.class);
        util.exportExcel(response, list, "设备预警设置数据");
    }

    /**
     * 获取设备预警设置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:deviceWarnSet:query')")
    @GetMapping(value = "/{tDeviceWarnId}")
    public AjaxResult getInfo(@PathVariable("tDeviceWarnId") Long tDeviceWarnId)
    {
        return success(deviceWarnSetService.getInfo(tDeviceWarnId));
    }

    /**
     * 新增设备预警设置
     */
    @PreAuthorize("@ss.hasPermi('system:deviceWarnSet:add')")
    @Log(title = "设备预警设置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DeviceWarnSet deviceWarnSet)
    {
        return toAjax(deviceWarnSetService.insert(deviceWarnSet));
    }

    /**
     * 修改设备预警设置
     */
    @PreAuthorize("@ss.hasPermi('system:deviceWarnSet:edit')")
    @Log(title = "设备预警设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DeviceWarnSet deviceWarnSet)
    {
        return toAjax(deviceWarnSetService.update(deviceWarnSet));
    }

    /**
     * 删除设备预警设置
     */
    @PreAuthorize("@ss.hasPermi('system:deviceWarnSet:remove')")
    @Log(title = "设备预警设置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{tDeviceWarnIds}")
    public AjaxResult remove(@PathVariable Long[] tDeviceWarnIds)
    {
        return toAjax(deviceWarnSetService.deleteByIds(tDeviceWarnIds));
    }
}
