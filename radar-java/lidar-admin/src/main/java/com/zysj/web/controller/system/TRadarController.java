package com.zysj.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;
import com.zysj.system.domain.TRadar;
import com.zysj.system.service.ITRadarService;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 雷达管理Controller
 * 
 * <AUTHOR>
 * @date 2024-10-17
 */
@Api(tags="雷达管理")
@RestController
@RequestMapping("/system/radar")
public class TRadarController extends BaseController
{
    @Autowired
    private ITRadarService tRadarService;

    /**
     * 查询雷达管理列表
     */
    @ApiOperation(value="查询雷达管理列表")
    @PreAuthorize("@ss.hasPermi('system:radar:list')")
    @GetMapping("/list")
    public TableDataInfo list(TRadar tRadar)
    {
        startPage();
        List<TRadar> list = tRadarService.selectTRadarList(tRadar);
        return getDataTable(list);
    }

    /**
     * 导出雷达管理列表
     */
    @ApiOperation(value="导出雷达管理列表")
    @PreAuthorize("@ss.hasPermi('system:radar:export')")
    @Log(title = "雷达管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TRadar tRadar)
    {
        List<TRadar> list = tRadarService.selectTRadarList(tRadar);
        ExcelUtil<TRadar> util = new ExcelUtil<TRadar>(TRadar.class);
        util.exportExcel(response, list, "雷达管理数据");
    }

    /**
     * 获取雷达管理详细信息
     */
    @ApiOperation(value="获取雷达管理详细信息")
    @PreAuthorize("@ss.hasPermi('system:radar:query')")
    @GetMapping(value = "/{tRadarId}")
    public AjaxResult getInfo(@PathVariable("tRadarId") Long tRadarId)
    {
        return success(tRadarService.selectTRadarByTRadarId(tRadarId));
    }

    /**
     * 新增雷达管理
     */
    @ApiOperation(value="新增雷达管理")
    @PreAuthorize("@ss.hasPermi('system:radar:add')")
    @Log(title = "雷达管理", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResult add(@RequestBody TRadar tRadar)
    {
        return toAjax(tRadarService.insertTRadar(tRadar));
    }

    /**
     * 修改雷达管理
     */
    @ApiOperation(value="修改雷达管理")
    @PreAuthorize("@ss.hasPermi('system:radar:edit')")
    @Log(title = "雷达管理", businessType = BusinessType.UPDATE)
    @PutMapping("edit")
    public AjaxResult edit(@RequestBody TRadar tRadar)
    {

        return toAjax(tRadarService.updateTRadar(tRadar));
    }

    /**
     * 删除雷达管理
     */
    @ApiOperation(value="删除雷达管理")
    @PreAuthorize("@ss.hasPermi('system:radar:remove')")
    @Log(title = "雷达管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{tRadarIds}")
    public AjaxResult remove(@PathVariable Long[] tRadarIds)
    {
        return toAjax(tRadarService.deleteTRadarByTRadarIds(tRadarIds));
    }
}
