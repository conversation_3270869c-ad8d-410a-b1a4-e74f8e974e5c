package com.zysj.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;
import com.zysj.system.domain.TRadarMode;
import com.zysj.system.service.ITRadarModeService;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 雷达工作模式管理Controller
 * 
 * <AUTHOR>
 * @date 2024-10-17
 */
@Api(tags="雷达工作模式管理")
@RestController
@RequestMapping("/system/mode")
public class TRadarModeController extends BaseController
{
    @Autowired
    private ITRadarModeService tRadarModeService;

    /**
     * 查询雷达工作模式管理列表
     */
    @ApiOperation(value="查询雷达工作模式管理列表")
    @PreAuthorize("@ss.hasPermi('system:mode:list')")
    @GetMapping("/list")
    public TableDataInfo list(TRadarMode tRadarMode)
    {
        startPage();
        List<TRadarMode> list = tRadarModeService.selectTRadarModeList(tRadarMode);
        return getDataTable(list);
    }

    /**
     * 导出雷达工作模式管理列表
     */
    @ApiOperation(value="导出雷达工作模式管理列表")
    @PreAuthorize("@ss.hasPermi('system:mode:export')")
    @Log(title = "雷达工作模式管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TRadarMode tRadarMode)
    {
        List<TRadarMode> list = tRadarModeService.selectTRadarModeList(tRadarMode);
        ExcelUtil<TRadarMode> util = new ExcelUtil<TRadarMode>(TRadarMode.class);
        util.exportExcel(response, list, "雷达工作模式管理数据");
    }

    /**
     * 获取雷达工作模式管理详细信息
     */
    @ApiOperation(value="获取雷达工作模式管理详细信息")
    @PreAuthorize("@ss.hasPermi('system:mode:query')")
    @GetMapping(value = "/{tRadarModeId}")
    public AjaxResult getInfo(@PathVariable("tRadarModeId") Long tRadarModeId)
    {
        return success(tRadarModeService.selectTRadarModeByTRadarModeId(tRadarModeId));
    }

    /**
     * 新增雷达工作模式管理
     */
    @ApiOperation(value="新增雷达工作模式管理")
    @PreAuthorize("@ss.hasPermi('system:mode:add')")
    @Log(title = "雷达工作模式管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TRadarMode tRadarMode)
    {
        return toAjax(tRadarModeService.insertTRadarMode(tRadarMode));
    }

    /**
     * 修改雷达工作模式管理
     */
    @ApiOperation(value="修改雷达工作模式管理")
    @PreAuthorize("@ss.hasPermi('system:mode:edit')")
    @Log(title = "雷达工作模式管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TRadarMode tRadarMode)
    {
        return toAjax(tRadarModeService.updateTRadarMode(tRadarMode));
    }

    /**
     * 删除雷达工作模式管理
     */
    @ApiOperation(value="删除雷达工作模式管理")
    @PreAuthorize("@ss.hasPermi('system:mode:remove')")
    @Log(title = "雷达工作模式管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{tRadarModeIds}")
    public AjaxResult remove(@PathVariable Long[] tRadarModeIds)
    {
        return toAjax(tRadarModeService.deleteTRadarModeByTRadarModeIds(tRadarModeIds));
    }
}
