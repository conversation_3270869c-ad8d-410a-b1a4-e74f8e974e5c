package com.zysj.web.controller.scan;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.scan.RadarMovement;
import com.zysj.system.service.scan.IRadarMovementService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 子任务运动轨迹Controller
 * 
 * <AUTHOR>
 * @date 2025-02-03
 */
@RestController
@RequestMapping("/scan/radarMovement")
public class RadarMovementController extends BaseController
{
    @Autowired
    private IRadarMovementService radarMovementService;

    /**
     * 查询子任务运动轨迹列表
     */
    @PreAuthorize("@ss.hasPermi('system:radarMovement:list')")
    @GetMapping("/list")
    public TableDataInfo list(RadarMovement radarMovement)
    {
        startPage();
        List<RadarMovement> list = radarMovementService.getList(radarMovement);
        return getDataTable(list);
    }

    /**
     * 查询子任务运动轨迹列表
     */
    @PreAuthorize("@ss.hasPermi('system:radarMovement:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(RadarMovement radarMovement)
    {
        List<RadarMovement> list = radarMovementService.getList(radarMovement);
        return getDataTable(list);
    }

    /**
     * 导出子任务运动轨迹列表
     */
    @PreAuthorize("@ss.hasPermi('system:radarMovement:export')")
    @Log(title = "子任务运动轨迹", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RadarMovement radarMovement)
    {
        List<RadarMovement> list = radarMovementService.getList(radarMovement);
        ExcelUtil<RadarMovement> util = new ExcelUtil<RadarMovement>(RadarMovement.class);
        util.exportExcel(response, list, "子任务运动轨迹数据");
    }

    /**
     * 获取子任务运动轨迹详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:radarMovement:query')")
    @GetMapping(value = "/{radarMovementId}")
    public AjaxResult getInfo(@PathVariable("radarMovementId") Long radarMovementId)
    {
        return success(radarMovementService.getInfo(radarMovementId));
    }

    /**
     * 新增子任务运动轨迹
     */
    @PreAuthorize("@ss.hasPermi('system:radarMovement:add')")
    @Log(title = "子任务运动轨迹", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RadarMovement radarMovement)
    {
        return toAjax(radarMovementService.insert(radarMovement));
    }

    /**
     * 修改子任务运动轨迹
     */
    @PreAuthorize("@ss.hasPermi('system:radarMovement:edit')")
    @Log(title = "子任务运动轨迹", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RadarMovement radarMovement)
    {
        return toAjax(radarMovementService.update(radarMovement));
    }

    /**
     * 删除子任务运动轨迹
     */
    @PreAuthorize("@ss.hasPermi('system:radarMovement:remove')")
    @Log(title = "子任务运动轨迹", businessType = BusinessType.DELETE)
	@DeleteMapping("/{radarMovementIds}")
    public AjaxResult remove(@PathVariable Long[] radarMovementIds)
    {
        return toAjax(radarMovementService.deleteByIds(radarMovementIds));
    }
}
