package com.zysj.web.controller.data;

import com.zysj.common.core.domain.AjaxResult;
import com.zysj.system.service.data.IGlobalDataService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import static com.zysj.common.core.domain.AjaxResult.success;

@RestController
@RequestMapping("/data/global")
public class GlobalDataController {

    @Resource
    private IGlobalDataService globalDataService;

    /**
     * 获取信息
     */
    @PreAuthorize("@ss.hasPermi('system:global:query')")
    @GetMapping(value = "/getGlobal")
    public AjaxResult getGlobal() {
        return success(globalDataService.getInfo());
    }

}
