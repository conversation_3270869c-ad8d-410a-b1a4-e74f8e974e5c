package com.zysj.web.controller.status;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.status.SystemAlarmSet;
import com.zysj.system.service.status.ISystemAlarmSetService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2025-02-03
 */
@RestController
@RequestMapping("/system/systemAlarmSet")
public class SystemAlarmSetController extends BaseController
{
    @Autowired
    private ISystemAlarmSetService systemAlarmSetService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmSet:list')")
    @GetMapping("/list")
    public TableDataInfo list(SystemAlarmSet systemAlarmSet)
    {
        startPage();
        List<SystemAlarmSet> list = systemAlarmSetService.getList(systemAlarmSet);
        return getDataTable(list);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmSet:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(SystemAlarmSet systemAlarmSet)
    {

        List<SystemAlarmSet> list = systemAlarmSetService.getList(systemAlarmSet);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmSet:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SystemAlarmSet systemAlarmSet)
    {
        List<SystemAlarmSet> list = systemAlarmSetService.getList(systemAlarmSet);
        ExcelUtil<SystemAlarmSet> util = new ExcelUtil<SystemAlarmSet>(SystemAlarmSet.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmSet:query')")
    @GetMapping(value = "/{systemAlarmSetId}")
    public AjaxResult getInfo(@PathVariable("systemAlarmSetId") Long systemAlarmSetId)
    {
        return success(systemAlarmSetService.getInfo(systemAlarmSetId));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmSet:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SystemAlarmSet systemAlarmSet)
    {
        return toAjax(systemAlarmSetService.insert(systemAlarmSet));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmSet:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SystemAlarmSet systemAlarmSet)
    {
        return toAjax(systemAlarmSetService.update(systemAlarmSet));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:systemAlarmSet:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{systemAlarmSetIds}")
    public AjaxResult remove(@PathVariable Long[] systemAlarmSetIds)
    {
        return toAjax(systemAlarmSetService.deleteByIds(systemAlarmSetIds));
    }
}
