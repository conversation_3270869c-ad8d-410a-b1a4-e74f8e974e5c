package com.zysj.web.controller.device;

import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.core.page.TableDataInfo;
import com.zysj.common.enums.BusinessType;
import com.zysj.common.utils.DateUtils;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.system.domain.device.RadarMark;
import com.zysj.system.service.device.IRadarMarkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 雷达标定Controller
 *
 * <AUTHOR>
 * @date 2025-02-12
 */
@Api(tags="雷达标定")
@RestController
@RequestMapping("/device/radarMark")
public class RadarMarkController extends BaseController {
    @Autowired
    private IRadarMarkService radarMarkService;

    /**
     * 查询雷达标定列表
     */
    @ApiOperation(value="查询雷达标定列表")
    @PreAuthorize("@ss.hasPermi('system:radarMark:list')")
    @GetMapping("/list")
    public TableDataInfo list(RadarMark radarMark) {
        startPage();
        List<RadarMark> list = radarMarkService.getList(radarMark);
        return getDataTable(list);
    }

    /**
     * 查询雷达标定列表
     */
    @ApiOperation(value="查询雷达标定列表")
    @PreAuthorize("@ss.hasPermi('system:radarMark:list')")
    @GetMapping("/listAll")
    public AjaxResult getAllList(RadarMark radarMark) {
        List<RadarMark> list = radarMarkService.getList(radarMark);
        return success(list);
    }


    /**
     * 导出雷达标定列表
     */
    @ApiOperation(value="导出雷达标定列表")
    @PreAuthorize("@ss.hasPermi('system:radarMark:export')")
    @Log(title = "雷达标定", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RadarMark radarMark) {
        List<RadarMark> list = radarMarkService.getList(radarMark);
        ExcelUtil<RadarMark> util = new ExcelUtil<RadarMark>(RadarMark.class);
        util.exportExcel(response, list, "雷达标定数据");
    }

    /**
     * 获取雷达标定详细信息
     */
    @ApiOperation(value="获取雷达标定详细信息")
    @PreAuthorize("@ss.hasPermi('system:radarMark:query')")
    @GetMapping(value = "/{radarMarkId}")
    public AjaxResult getInfo(@PathVariable("radarMarkId") Long radarMarkId) {
        return success(radarMarkService.getInfo(radarMarkId));
    }

    /**
     * 新增雷达标定
     */
    @ApiOperation(value="新增雷达标定")
    @PreAuthorize("@ss.hasPermi('system:radarMark:add')")
    @Log(title = "雷达标定", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RadarMark radarMark) {
        radarMark.setCreateBy(getUsername());
        radarMark.setUserId(String.valueOf(getUserId()));
        radarMark.setMarkTime(DateUtils.dateTimeNow(DateUtils.YYYY_MM_DD_HH_MM_SS));
        return toAjax(radarMarkService.insert(radarMark));
    }

    /**
     * 修改雷达标定
     */
    @ApiOperation(value="修改雷达标定")
    @PreAuthorize("@ss.hasPermi('system:radarMark:edit')")
    @Log(title = "雷达标定", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RadarMark radarMark) {
        radarMark.setUpdateBy(getUsername());
        return toAjax(radarMarkService.update(radarMark));
    }

    /**
     * 删除雷达标定
     */
    @ApiOperation(value="删除雷达标定")
    @PreAuthorize("@ss.hasPermi('system:radarMark:remove')")
    @Log(title = "雷达标定", businessType = BusinessType.DELETE)
    @DeleteMapping("/{radarMarkIds}")
    public AjaxResult remove(@PathVariable Long[] radarMarkIds) {
        return toAjax(radarMarkService.deleteByIds(radarMarkIds));
    }

    /**
     * 雷达标定推送
     */
    @ApiOperation(value="雷达标定推送")
    @PreAuthorize("@ss.hasPermi('system:radarMark:push')")
    @Log(title = "雷达标定推送", businessType = BusinessType.PUSH)
    @PostMapping("/push")
    public AjaxResult push(@RequestBody RadarMark radarMark) throws Exception {
        return toAjax(radarMarkService.push(radarMark));
    }
}
