package com.zysj.web.controller.maintenance;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.maintenance.MaintenRecordDetail;
import com.zysj.system.service.maintenance.IMaintenRecordDetailService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 维修明细Controller
 * 
 * <AUTHOR>
 * @date 2025-02-03
 */
@RestController
@RequestMapping("/maintenance/maintenRecordDetail")
public class MaintenRecordDetailController extends BaseController
{
    @Autowired
    private IMaintenRecordDetailService maintenRecordDetailService;

    /**
     * 查询维修明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:maintenRecordDetail:list')")
    @GetMapping("/list")
    public TableDataInfo list(MaintenRecordDetail maintenRecordDetail)
    {
        startPage();
        List<MaintenRecordDetail> list = maintenRecordDetailService.getList(maintenRecordDetail);
        return getDataTable(list);
    }

    /**
     * 查询维修明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:maintenRecordDetail:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(MaintenRecordDetail maintenRecordDetail)
    {

        List<MaintenRecordDetail> list = maintenRecordDetailService.getList(maintenRecordDetail);
        return getDataTable(list);
    }


    /**
     * 导出维修明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:maintenRecordDetail:export')")
    @Log(title = "维修明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MaintenRecordDetail maintenRecordDetail)
    {
        List<MaintenRecordDetail> list = maintenRecordDetailService.getList(maintenRecordDetail);
        ExcelUtil<MaintenRecordDetail> util = new ExcelUtil<MaintenRecordDetail>(MaintenRecordDetail.class);
        util.exportExcel(response, list, "维修明细数据");
    }

    /**
     * 获取维修明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:maintenRecordDetail:query')")
    @GetMapping(value = "/{maintenRecordDetailsId}")
    public AjaxResult getInfo(@PathVariable("maintenRecordDetailsId") Long maintenRecordDetailsId)
    {
        return success(maintenRecordDetailService.getInfo(maintenRecordDetailsId));
    }

    /**
     * 新增维修明细
     */
    @PreAuthorize("@ss.hasPermi('system:maintenRecordDetail:add')")
    @Log(title = "维修明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MaintenRecordDetail maintenRecordDetail)
    {
        return toAjax(maintenRecordDetailService.insert(maintenRecordDetail));
    }

    /**
     * 修改维修明细
     */
    @PreAuthorize("@ss.hasPermi('system:maintenRecordDetail:edit')")
    @Log(title = "维修明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MaintenRecordDetail maintenRecordDetail)
    {
        return toAjax(maintenRecordDetailService.update(maintenRecordDetail));
    }

    /**
     * 删除维修明细
     */
    @PreAuthorize("@ss.hasPermi('system:maintenRecordDetail:remove')")
    @Log(title = "维修明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{maintenRecordDetailsIds}")
    public AjaxResult remove(@PathVariable Long[] maintenRecordDetailsIds)
    {
        return toAjax(maintenRecordDetailService.deleteByIds(maintenRecordDetailsIds));
    }
}
