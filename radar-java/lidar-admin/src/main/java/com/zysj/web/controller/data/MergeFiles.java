package com.zysj.web.controller.data;

import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

public class MergeFiles {

    public static void main(String[] args) throws IOException {
        // 读取所有a文件和b文件
        List<Map<String, Object>> data56 = readFiles("I:\\工作内容\\2025-03\\Desktop\\56\\平均水平风速56_20250310_60");
        List<Map<String, Object>> data57 = readFiles("I:\\工作内容\\2025-03\\Desktop\\57\\平均水平风速57_20250310_60");
        // 合并数据
        List<Map<String, Object>> mergedData56 = mergeData56(data56);
        List<Map<String, Object>> mergedData57 = mergeData57(data57);
        // 生成Excel文件
        writeToExcel(mergedData56, mergedData57, "I:\\工作内容\\2025-03\\Desktop\\平均垂直风速.xlsx");
    }

    private static List<Map<String, Object>> readFiles(String prefix) throws IOException {
        List<Map<String, Object>> data = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            String fileName = prefix + i + ".csv";
            File file = new File(fileName);
            if (file.exists()) {
                data.addAll(readCsv(file));
            }
        }
        return data;
    }

    private static List<Map<String, Object>> readCsv(File file) throws IOException {
        List<Map<String, Object>> data = new ArrayList<>();
        try (Scanner scanner = new Scanner(file)) {
            while (scanner.hasNextLine()) {
                String line = scanner.nextLine();
                String[] values = line.split(",");
                Map<String, Object> row = new HashMap<>();
                row.put("Date Time", values[0]);
                for (int i = 1; i < values.length; i++) {
                    row.put(String.valueOf(40 + (i - 1) * 20), Double.parseDouble(values[i]));
                }
                data.add(row);
            }
        }
        return data;
    }

    private static List<Map<String, Object>> mergeData56(List<Map<String, Object>> aData) {
        LinkedHashMap<String, Map<String, Object>> mergedMap = new LinkedHashMap<>();
        for (Map<String, Object> aRow : aData) {
            String dateTime = (String) aRow.get("Date Time");
            if (null != dateTime && !dateTime.equals("Date Time")){
                mergedMap.putIfAbsent(dateTime, new HashMap<>());
                mergedMap.get(dateTime).putAll(aRow);
            }
        }
        return new ArrayList<>(mergedMap.values());
    }

    private static List<Map<String, Object>> mergeData57(List<Map<String, Object>> aData) {
        LinkedHashMap<String, Map<String, Object>> mergedMap = new LinkedHashMap<>();
        for (Map<String, Object> aRow : aData) {
            String dateTime = (String) aRow.get("Date Time");
            if (null != dateTime && !dateTime.equals("Date Time")){
                mergedMap.putIfAbsent(dateTime, new HashMap<>());
                mergedMap.get(dateTime).putAll(aRow);
            }
        }
        return new ArrayList<>(mergedMap.values());
    }

    private static void writeToExcel(List<Map<String, Object>> data56, List<Map<String, Object>> data57, String fileName) throws IOException {
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("Merged Data");
        // 创建表头
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("时间");
        int colIndex = 1;
        for (int i = 40; i <= 1000; i += 20) {
            headerRow.createCell(colIndex++).setCellValue("56-" + i);
            headerRow.createCell(colIndex++).setCellValue("57-" + i);
        }
        // 填充数据
        int a = 0;
        int rowIndex = 1;
        for (Map<String, Object> row : data56) {
            Row excelRow = sheet.createRow(rowIndex++);
            excelRow.createCell(0).setCellValue((String) row.get("Date Time"));
            colIndex = 1;
            for (int i = 40; i <= 1000; i += 20) {
                if (data57.get(a).get("Date Time").equals(row.get("Date Time"))){
                    excelRow.createCell(colIndex++).setCellValue((Double) data57.get(a).getOrDefault(String.valueOf(i), 0.0));
                    excelRow.createCell(colIndex++).setCellValue((Double) row.getOrDefault(String.valueOf(i), 0.0));
                }
            }
            a++;
        }
        // 写入文件
        try (FileOutputStream fileOut = new FileOutputStream(fileName)) {
            workbook.write(fileOut);
        }
        workbook.close();
    }
}