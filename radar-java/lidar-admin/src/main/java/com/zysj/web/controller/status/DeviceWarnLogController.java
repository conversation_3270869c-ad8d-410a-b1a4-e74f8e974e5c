package com.zysj.web.controller.status;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.status.DeviceWarnLog;
import com.zysj.system.service.status.IDeviceWarnLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 设备预警日志Controller
 * 
 * <AUTHOR>
 * @date 2025-02-03
 */
@RestController
@RequestMapping("/status/deviceWarnLog")
public class DeviceWarnLogController extends BaseController
{
    @Autowired
    private IDeviceWarnLogService deviceWarnLogService;

    /**
     * 查询设备预警日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:deviceWarnLog:list')")
    @GetMapping("/list")
    public TableDataInfo list(DeviceWarnLog deviceWarnLog)
    {
        startPage();
        List<DeviceWarnLog> list = deviceWarnLogService.getList(deviceWarnLog);
        return getDataTable(list);
    }

    /**
     * 查询设备预警日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:deviceWarnLog:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(DeviceWarnLog deviceWarnLog)
    {

        List<DeviceWarnLog> list = deviceWarnLogService.getList(deviceWarnLog);
        return getDataTable(list);
    }

    /**
     * 导出设备预警日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:deviceWarnLog:export')")
    @Log(title = "设备预警日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DeviceWarnLog deviceWarnLog)
    {
        List<DeviceWarnLog> list = deviceWarnLogService.getList(deviceWarnLog);
        ExcelUtil<DeviceWarnLog> util = new ExcelUtil<DeviceWarnLog>(DeviceWarnLog.class);
        util.exportExcel(response, list, "设备预警日志数据");
    }

    /**
     * 获取设备预警日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:deviceWarnLog:query')")
    @GetMapping(value = "/{tDeviceWarnLogId}")
    public AjaxResult getInfo(@PathVariable("tDeviceWarnLogId") Long tDeviceWarnLogId)
    {
        return success(deviceWarnLogService.getInfo(tDeviceWarnLogId));
    }

    /**
     * 新增设备预警日志
     */
    @PreAuthorize("@ss.hasPermi('system:deviceWarnLog:add')")
    @Log(title = "设备预警日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DeviceWarnLog deviceWarnLog)
    {
        return toAjax(deviceWarnLogService.insert(deviceWarnLog));
    }

    /**
     * 修改设备预警日志
     */
    @PreAuthorize("@ss.hasPermi('system:deviceWarnLog:edit')")
    @Log(title = "设备预警日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DeviceWarnLog deviceWarnLog)
    {
        return toAjax(deviceWarnLogService.update(deviceWarnLog));
    }

    /**
     * 删除设备预警日志
     */
    @PreAuthorize("@ss.hasPermi('system:deviceWarnLog:remove')")
    @Log(title = "设备预警日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{tDeviceWarnLogIds}")
    public AjaxResult remove(@PathVariable Long[] tDeviceWarnLogIds)
    {
        return toAjax(deviceWarnLogService.deleteByIds(tDeviceWarnLogIds));
    }
}
