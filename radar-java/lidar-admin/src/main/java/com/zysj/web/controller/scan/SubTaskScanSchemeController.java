package com.zysj.web.controller.scan;

import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.core.page.TableDataInfo;
import com.zysj.common.enums.BusinessType;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.system.domain.scan.SubTaskScanScheme;
import com.zysj.system.service.scan.ISubTaskScanSchemeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 任务参数配置Controller
 * 
 * <AUTHOR>
 * @date 2025-02-06
 */
@RestController
@RequestMapping("/scan/subTaskScanScheme")
public class SubTaskScanSchemeController extends BaseController
{
    @Autowired
    private ISubTaskScanSchemeService subTaskScanSchemeService;

    /**
     * 查询任务参数配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:subTaskScanScheme:list')")
    @GetMapping("/list")
    public TableDataInfo list(SubTaskScanScheme subTaskScanScheme)
    {
        startPage();
        List<SubTaskScanScheme> list = subTaskScanSchemeService.getList(subTaskScanScheme);
        return getDataTable(list);
    }
    /**
     * 查询任务参数配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:subTaskScanScheme:list')")
    @GetMapping("/listAll")
    public AjaxResult getAllList(SubTaskScanScheme subTaskScanScheme)
    {
        List<SubTaskScanScheme> list = subTaskScanSchemeService.getList(subTaskScanScheme);
        return success(list);
    }


    /**
     * 导出任务参数配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:subTaskScanScheme:export')")
    @Log(title = "任务参数配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SubTaskScanScheme subTaskScanScheme)
    {
        List<SubTaskScanScheme> list = subTaskScanSchemeService.getList(subTaskScanScheme);
        ExcelUtil<SubTaskScanScheme> util = new ExcelUtil<SubTaskScanScheme>(SubTaskScanScheme.class);
        util.exportExcel(response, list, "任务参数配置数据");
    }

    /**
     * 获取任务参数配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:subTaskScanScheme:query')")
    @GetMapping(value = "/{subTaskScanSchemeId}")
    public AjaxResult getInfo(@PathVariable("subTaskScanSchemeId") Long subTaskScanSchemeId)
    {
        return success(subTaskScanSchemeService.getInfo(subTaskScanSchemeId));
    }

    /**
     * 获取任务参数配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:scheme:query')")
    @GetMapping(value = "/getInfoBysSanSubTaskId/{sanSubTaskId}")
    public AjaxResult getInfoBysSanSubTaskId(@PathVariable("sanSubTaskId") String sanSubTaskId) {
        return success(subTaskScanSchemeService.getInfoBysSanSubTaskId(sanSubTaskId));
    }

    /**
     * 新增任务参数配置
     */
    @PreAuthorize("@ss.hasPermi('system:subTaskScanScheme:add')")
    @Log(title = "任务参数配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SubTaskScanScheme subTaskScanScheme)
    {
        return toAjax(subTaskScanSchemeService.insert(subTaskScanScheme));
    }

    /**
     * 修改任务参数配置
     */
    @PreAuthorize("@ss.hasPermi('system:subTaskScanScheme:edit')")
    @Log(title = "任务参数配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SubTaskScanScheme subTaskScanScheme)
    {
        return toAjax(subTaskScanSchemeService.update(subTaskScanScheme));
    }

    /**
     * 删除任务参数配置
     */
    @PreAuthorize("@ss.hasPermi('system:subTaskScanScheme:remove')")
    @Log(title = "任务参数配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{subTaskScanSchemeIds}")
    public AjaxResult remove(@PathVariable Long[] subTaskScanSchemeIds)
    {
        return toAjax(subTaskScanSchemeService.deleteByIds(subTaskScanSchemeIds));
    }
}
