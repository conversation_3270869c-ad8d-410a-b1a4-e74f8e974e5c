package com.zysj.web.controller.file;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.file.RadarFile;
import com.zysj.system.service.file.IRadarFileService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 文件管理Controller
 * 
 * <AUTHOR>
 * @date 2025-02-08
 */
@RestController
@RequestMapping("/file/radarFile")
public class RadarFileController extends BaseController
{
    @Autowired
    private IRadarFileService radarFileService;

    /**
     * 查询文件管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:radarFile:list')")
    @GetMapping("/list")
    public TableDataInfo list(RadarFile radarFile)
    {
        startPage();
        List<RadarFile> list = radarFileService.getList(radarFile);
        return getDataTable(list);
    }
    /**
     * 查询文件管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:radarFile:list')")
    @GetMapping("/listAll")
    public AjaxResult getAllList(RadarFile radarFile)
    {
        List<RadarFile> list = radarFileService.getList(radarFile);
        return success(list);
    }


    /**
     * 导出文件管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:radarFile:export')")
    @Log(title = "文件管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RadarFile radarFile)
    {
        List<RadarFile> list = radarFileService.getList(radarFile);
        ExcelUtil<RadarFile> util = new ExcelUtil<RadarFile>(RadarFile.class);
        util.exportExcel(response, list, "文件管理数据");
    }

    /**
     * 获取文件管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:radarFile:query')")
    @GetMapping(value = "/{radarFileId}")
    public AjaxResult getInfo(@PathVariable("radarFileId") Long radarFileId)
    {
        return success(radarFileService.getInfo(radarFileId));
    }

    /**
     * 新增文件管理
     */
//    @PreAuthorize("@ss.hasPermi('system:radarFile:add')")
    @Log(title = "文件管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RadarFile radarFile)
    {
        return toAjax(radarFileService.insert(radarFile));
    }

    /**
     * 修改文件管理
     */
//    @PreAuthorize("@ss.hasPermi('system:radarFile:edit')")
    @Log(title = "文件管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RadarFile radarFile)
    {
        return toAjax(radarFileService.update(radarFile));
    }

    /**
     * 删除文件管理
     */
    @PreAuthorize("@ss.hasPermi('system:radarFile:remove')")
    @Log(title = "文件管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{radarFileIds}")
    public AjaxResult remove(@PathVariable Long[] radarFileIds)
    {
        return toAjax(radarFileService.deleteByIds(radarFileIds));
    }
}
