package com.zysj.web.controller.scan;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.scan.ScanType;
import com.zysj.system.service.scan.IScanTypeService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2025-02-03
 */
@RestController
@RequestMapping("/scan/scanType")
public class ScanTypeController extends BaseController
{
    @Autowired
    private IScanTypeService scanTypeService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:scanType:list')")
    @GetMapping("/list")
    public TableDataInfo list(ScanType scanType)
    {
        startPage();
        List<ScanType> list = scanTypeService.getList(scanType);
        return getDataTable(list);
    }

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:scanType:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(ScanType scanType)
    {
        List<ScanType> list = scanTypeService.getList(scanType);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:scanType:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScanType scanType)
    {
        List<ScanType> list = scanTypeService.getList(scanType);
        ExcelUtil<ScanType> util = new ExcelUtil<ScanType>(ScanType.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:scanType:query')")
    @GetMapping(value = "/{scanTypelId}")
    public AjaxResult getInfo(@PathVariable("scanTypelId") Long scanTypelId)
    {
        return success(scanTypeService.getInfo(scanTypelId));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:scanType:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ScanType scanType)
    {
        return toAjax(scanTypeService.insert(scanType));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:scanType:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ScanType scanType)
    {
        return toAjax(scanTypeService.update(scanType));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:scanType:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{scanTypelIds}")
    public AjaxResult remove(@PathVariable Long[] scanTypelIds)
    {
        return toAjax(scanTypeService.deleteByIds(scanTypelIds));
    }

    /**
     * 扫描类型推送
     */
    @PreAuthorize("@ss.hasPermi('system:scanType:push')")
    @Log(title = "扫描类型推送", businessType = BusinessType.PUSH)
    @PostMapping("/batchPushScanTypes")
    public AjaxResult batchPushScanTypes(@RequestBody List<Long> scanTypeIds) throws Exception {
        return toAjax(scanTypeService.batchPushScanTypes(scanTypeIds));
    }
}
