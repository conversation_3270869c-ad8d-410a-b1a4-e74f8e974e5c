package com.zysj.web.controller.status;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.status.DataWarnSet;
import com.zysj.system.service.status.IDataWarnSetService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 采集数据预警设置Controller
 * 
 * <AUTHOR>
 * @date 2025-02-03
 */
@RestController
@RequestMapping("/status/dataWarnSet")
public class DataWarnSetController extends BaseController
{
    @Autowired
    private IDataWarnSetService dataWarnSetService;

    /**
     * 查询采集数据预警设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataWarnSet:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataWarnSet dataWarnSet)
    {
        startPage();
        List<DataWarnSet> list = dataWarnSetService.getList(dataWarnSet);
        return getDataTable(list);
    }

    /**
     * 查询采集数据预警设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataWarnSet:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(DataWarnSet dataWarnSet)
    {

        List<DataWarnSet> list = dataWarnSetService.getList(dataWarnSet);
        return getDataTable(list);
    }

    /**
     * 导出采集数据预警设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataWarnSet:export')")
    @Log(title = "采集数据预警设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataWarnSet dataWarnSet)
    {
        List<DataWarnSet> list = dataWarnSetService.getList(dataWarnSet);
        ExcelUtil<DataWarnSet> util = new ExcelUtil<DataWarnSet>(DataWarnSet.class);
        util.exportExcel(response, list, "采集数据预警设置数据");
    }

    /**
     * 获取采集数据预警设置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dataWarnSet:query')")
    @GetMapping(value = "/{tDataWarnId}")
    public AjaxResult getInfo(@PathVariable("tDataWarnId") Long tDataWarnId)
    {
        return success(dataWarnSetService.getInfo(tDataWarnId));
    }

    /**
     * 新增采集数据预警设置
     */
    @PreAuthorize("@ss.hasPermi('system:dataWarnSet:add')")
    @Log(title = "采集数据预警设置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataWarnSet dataWarnSet)
    {
        return toAjax(dataWarnSetService.insert(dataWarnSet));
    }

    /**
     * 修改采集数据预警设置
     */
    @PreAuthorize("@ss.hasPermi('system:dataWarnSet:edit')")
    @Log(title = "采集数据预警设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataWarnSet dataWarnSet)
    {
        return toAjax(dataWarnSetService.update(dataWarnSet));
    }

    /**
     * 删除采集数据预警设置
     */
    @PreAuthorize("@ss.hasPermi('system:dataWarnSet:remove')")
    @Log(title = "采集数据预警设置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{tDataWarnIds}")
    public AjaxResult remove(@PathVariable Long[] tDataWarnIds)
    {
        return toAjax(dataWarnSetService.deleteByIds(tDataWarnIds));
    }
}
