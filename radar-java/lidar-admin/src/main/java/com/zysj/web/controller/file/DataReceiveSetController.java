package com.zysj.web.controller.file;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.file.DataReceiveSet;
import com.zysj.system.domain.vo.DataReceiveSetVo;
import com.zysj.system.service.file.IDataReceiveSetService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 数据接收设置Controller
 * 
 * <AUTHOR>
 * @date 2025-02-03
 */
@RestController
@RequestMapping("/file/dataReceiveSet")
public class DataReceiveSetController extends BaseController
{
    @Autowired
    private IDataReceiveSetService dataReceiveSetService;

    /**
     * 查询数据接收设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataReceiveSet:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataReceiveSet dataReceiveSet)
    {
        startPage();
        List<DataReceiveSet> list = dataReceiveSetService.getList(dataReceiveSet);
        return getDataTable(list);
    }

    /**
     * 查询数据接收设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataReceiveSet:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(DataReceiveSet dataReceiveSet)
    {

        List<DataReceiveSet> list = dataReceiveSetService.getList(dataReceiveSet);
        return getDataTable(list);
    }

    /**
     * 导出数据接收设置列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataReceiveSet:export')")
    @Log(title = "数据接收设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataReceiveSet dataReceiveSet)
    {
        List<DataReceiveSet> list = dataReceiveSetService.getList(dataReceiveSet);
        ExcelUtil<DataReceiveSet> util = new ExcelUtil<DataReceiveSet>(DataReceiveSet.class);
        util.exportExcel(response, list, "数据接收设置数据");
    }

    /**
     * 获取数据接收设置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dataReceiveSet:query')")
    @GetMapping(value = "/{dataReceiveSetId}")
    public AjaxResult getInfo(@PathVariable("dataReceiveSetId") Long dataReceiveSetId)
    {
        return success(dataReceiveSetService.getInfo(dataReceiveSetId));
    }

    /**
     * 新增数据接收设置
     */
    @PreAuthorize("@ss.hasPermi('system:dataReceiveSet:add')")
    @Log(title = "数据接收设置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataReceiveSet dataReceiveSet)
    {
        return toAjax(dataReceiveSetService.insert(dataReceiveSet));
    }

    /**
     * 修改数据接收设置
     */
    @PreAuthorize("@ss.hasPermi('system:dataReceiveSet:edit')")
    @Log(title = "数据接收设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataReceiveSet dataReceiveSet)
    {
        return toAjax(dataReceiveSetService.update(dataReceiveSet));
    }

    /**
     * 删除数据接收设置
     */
    @PreAuthorize("@ss.hasPermi('system:dataReceiveSet:remove')")
    @Log(title = "数据接收设置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dataReceiveSetIds}")
    public AjaxResult remove(@PathVariable Long[] dataReceiveSetIds)
    {
        return toAjax(dataReceiveSetService.deleteByIds(dataReceiveSetIds));
    }

    /**
     * 数据接收信息推送
     */
    @PreAuthorize("@ss.hasPermi('system:dataReceiveSet:push')")
    @Log(title = "数据接收设置", businessType = BusinessType.PUSH)
    @PostMapping("/push")
    public AjaxResult push(@RequestBody DataReceiveSetVo dataReceiveSetVo) throws Exception{
        return toAjax(dataReceiveSetService.push(dataReceiveSetVo));
    }
}
