package com.zysj.web.controller.status;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.status.DataWarnLog;
import com.zysj.system.service.status.IDataWarnLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 采集数据预警日志Controller
 * 
 * <AUTHOR>
 * @date 2025-02-03
 */
@RestController
@RequestMapping("/status/dataWarnLog")
public class DataWarnLogController extends BaseController
{
    @Autowired
    private IDataWarnLogService dataWarnLogService;

    /**
     * 查询采集数据预警日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataWarnLog:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataWarnLog dataWarnLog)
    {
        startPage();
        List<DataWarnLog> list = dataWarnLogService.getList(dataWarnLog);
        return getDataTable(list);
    }

    /**
     * 查询采集数据预警日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataWarnLog:list')")
    @GetMapping("/listAll")
    public TableDataInfo getAllList(DataWarnLog dataWarnLog)
    {

        List<DataWarnLog> list = dataWarnLogService.getList(dataWarnLog);
        return getDataTable(list);
    }

    /**
     * 导出采集数据预警日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataWarnLog:export')")
    @Log(title = "采集数据预警日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataWarnLog dataWarnLog)
    {
        List<DataWarnLog> list = dataWarnLogService.getList(dataWarnLog);
        ExcelUtil<DataWarnLog> util = new ExcelUtil<DataWarnLog>(DataWarnLog.class);
        util.exportExcel(response, list, "采集数据预警日志数据");
    }

    /**
     * 获取采集数据预警日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dataWarnLog:query')")
    @GetMapping(value = "/{tDataWarnLogId}")
    public AjaxResult getInfo(@PathVariable("tDataWarnLogId") Long tDataWarnLogId)
    {
        return success(dataWarnLogService.getInfo(tDataWarnLogId));
    }

    /**
     * 新增采集数据预警日志
     */
    @PreAuthorize("@ss.hasPermi('system:dataWarnLog:add')")
    @Log(title = "采集数据预警日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataWarnLog dataWarnLog)
    {
        return toAjax(dataWarnLogService.insert(dataWarnLog));
    }

    /**
     * 修改采集数据预警日志
     */
    @PreAuthorize("@ss.hasPermi('system:dataWarnLog:edit')")
    @Log(title = "采集数据预警日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataWarnLog dataWarnLog)
    {
        return toAjax(dataWarnLogService.update(dataWarnLog));
    }

    /**
     * 删除采集数据预警日志
     */
    @PreAuthorize("@ss.hasPermi('system:dataWarnLog:remove')")
    @Log(title = "采集数据预警日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{tDataWarnLogIds}")
    public AjaxResult remove(@PathVariable Long[] tDataWarnLogIds)
    {
        return toAjax(dataWarnLogService.deleteByIds(tDataWarnLogIds));
    }
}
