package com.zysj.web.controller.log;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.zysj.system.domain.log.DataHandleLog;
import com.zysj.system.service.log.IDataHandleLogService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.enums.BusinessType;

import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2025-02-09
 */
@RestController
@RequestMapping("/log/dataHandleLog")
public class DataHandleLogController extends BaseController
{
    @Autowired
    private IDataHandleLogService dataHandleLogService;

    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataHandleLog:list')")
    @GetMapping("/list")
    public TableDataInfo list(DataHandleLog dataHandleLog)
    {
        startPage();
        List<DataHandleLog> list = dataHandleLogService.getList(dataHandleLog);
        return getDataTable(list);
    }
    /**
     * 查询【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataHandleLog:list')")
    @GetMapping("/listAll")
    public AjaxResult getAllList(DataHandleLog dataHandleLog)
    {
        List<DataHandleLog> list = dataHandleLogService.getList(dataHandleLog);
        return success(list);
    }


    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataHandleLog:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DataHandleLog dataHandleLog)
    {
        List<DataHandleLog> list = dataHandleLogService.getList(dataHandleLog);
        ExcelUtil<DataHandleLog> util = new ExcelUtil<DataHandleLog>(DataHandleLog.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dataHandleLog:query')")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId)
    {
        return success(dataHandleLogService.getInfo(logId));
    }

    /**
     * 新增【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:dataHandleLog:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DataHandleLog dataHandleLog)
    {
        return toAjax(dataHandleLogService.insert(dataHandleLog));
    }

    /**
     * 修改【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:dataHandleLog:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DataHandleLog dataHandleLog)
    {
        return toAjax(dataHandleLogService.update(dataHandleLog));
    }

    /**
     * 删除【请填写功能名称】
     */
    @PreAuthorize("@ss.hasPermi('system:dataHandleLog:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
	@DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds)
    {
        return toAjax(dataHandleLogService.deleteByIds(logIds));
    }
}
