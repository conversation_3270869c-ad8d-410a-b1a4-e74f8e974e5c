package com.zysj.web.controller.scan;

import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.core.page.TableDataInfo;
import com.zysj.common.enums.BusinessType;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.system.domain.scan.ScanTask;
import com.zysj.system.domain.vo.CurrentScanInfoVo;
import com.zysj.system.service.scan.IScanTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

//import com.zysj.web.controller.config.WebxEUrl;

/**
 * 主任务Controller
 *
 * <AUTHOR>
 * @date 2025-01-26
 */
@Configurable
@RestController
@RequestMapping("/scan/scanTask")
public class ScanTaskController extends BaseController {
    @Autowired
    private IScanTaskService scanTaskService;

//    @Value("${ladar.ip}")
//    private String ip;
//
//    @Value("${ladar.WindLidar.stop}")
//    private String urlPatterns;


    /**
     * 查询主任务列表
     */
    @PreAuthorize("@ss.hasPermi('scan:task:list')")
    @GetMapping("/list")
    public TableDataInfo list(ScanTask scanTask) {
        startPage();
        List<ScanTask> list = scanTaskService.getList(scanTask);
        return getDataTable(list);
    }

    /**
     * 查询主任务列表
     */
    @PreAuthorize("@ss.hasPermi('scan:task:list')")
    @GetMapping("/listAll")
    public TableDataInfo getALlList(ScanTask scanTask) {
        List<ScanTask> list = scanTaskService.getList(scanTask);
        return getDataTable(list);
    }

    /**
     * 开始任务
     */
//    @PreAuthorize("@ss.hasPermi('scan:task:start')")
    @PostMapping("/start")
    public AjaxResult start(@RequestBody ScanTask scanTask) {

        return success(scanTaskService.start(scanTask));
    }

    /**
     * 结束任务
     */
//    @PreAuthorize("@ss.hasPermi('scan:task:stop')")
    @PostMapping("/stop")
    public AjaxResult stop() throws Exception {
        return success(scanTaskService.stop());
    }


    /**
     * 结束任务
     */
    @PreAuthorize("@ss.hasPermi('scan:task:stop')")
    @PostMapping("/stopByTask")
    public AjaxResult stopByTask(ScanTask scanTask) {
        return toAjax(scanTaskService.stopByTask(scanTask));
    }

    /**
     * 获取当前任务
     */
//    @PreAuthorize("@ss.hasPermi('scan:task:getCurrentTask')")
    @GetMapping("/getCurrentTask")
    public CurrentScanInfoVo getCurrentTask(@RequestParam(required = false) Date endTime)
    {
        return scanTaskService.getCurrentTask(endTime);
    }

    /**
     * 导出主任务列表
     */
    @PreAuthorize("@ss.hasPermi('scan:task:export')")
    @Log(title = "主任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ScanTask scanTask) {
        List<ScanTask> list = scanTaskService.getList(scanTask);
        ExcelUtil<ScanTask> util = new ExcelUtil<ScanTask>(ScanTask.class);
        util.exportExcel(response, list, "主任务数据");
    }

    /**
     * 获取主任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('scan:task:query')")
    @GetMapping(value = "/{scanTaskId}")
    public AjaxResult getInfo(@PathVariable("scanTaskId") String scanTaskId) {
        return success(scanTaskService.getInfo(scanTaskId));
    }

    /**
     * 新增主任务
     */
    @PreAuthorize("@ss.hasPermi('scan:task:add')")
    @Log(title = "主任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ScanTask scanTask) {
        return toAjax(scanTaskService.insert(scanTask));
    }

    /**
     * 修改主任务
     */
    @PreAuthorize("@ss.hasPermi('scan:task:edit')")
    @Log(title = "主任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ScanTask scanTask) {
        return toAjax(scanTaskService.update(scanTask));
    }

    /**
     * 删除主任务
     */
    @PreAuthorize("@ss.hasPermi('scan:task:remove')")
    @Log(title = "主任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{scanTaskIds}")
    public AjaxResult remove(@PathVariable String[] scanTaskIds) {
        return toAjax(scanTaskService.deleteByIds(scanTaskIds));
    }

    /**
     * 复制任务
     */
    @PreAuthorize("@ss.hasPermi('scan:task:edit')")
    @Log(title = "复制任务", businessType = BusinessType.UPDATE)
    @PostMapping("/copyTasks/{scanTaskIds}")
    public AjaxResult copyTasks(@PathVariable String[] scanTaskIds) {
        return toAjax(scanTaskService.copyTasks(scanTaskIds));
    }
}
