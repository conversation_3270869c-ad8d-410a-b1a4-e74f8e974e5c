package com.zysj.web.controller.system;

import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.core.page.TableDataInfo;
import com.zysj.common.enums.BusinessType;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.system.domain.TDevice;
import com.zysj.system.service.ITDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备管理Controller
 *
 * <AUTHOR>
 * @date 2024-10-17
 */
@Api(tags="设备管理")
@RestController
@RequestMapping("/system/device")
public class TDeviceController extends BaseController {
    @Autowired
    private ITDeviceService tDeviceService;

    /**
     * 查询设备管理列表
     */
    @ApiOperation(value="查询设备管理列表")
    @PreAuthorize("@ss.hasPermi('system:device:list')")
    @GetMapping("/list")
    public TableDataInfo list(TDevice tDevice) {
        startPage();
        List<TDevice> list = tDeviceService.selectTDeviceList(tDevice);
        return getDataTable(list);
    }

    /**
     * 导出设备管理列表
     */
    @ApiOperation(value="导出设备管理列表")
    @PreAuthorize("@ss.hasPermi('system:device:export')")
    @Log(title = "设备管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TDevice tDevice) {
        List<TDevice> list = tDeviceService.selectTDeviceList(tDevice);
        ExcelUtil<TDevice> util = new ExcelUtil<TDevice>(TDevice.class);
        util.exportExcel(response, list, "设备管理数据");
    }

    /**
     * 获取设备管理详细信息
     */
    @ApiOperation(value="获取设备管理详细信息")
    @PreAuthorize("@ss.hasPermi('system:device:query')")
    @GetMapping(value = "/{tDeviceId}")
    public AjaxResult getInfo(@PathVariable("tDeviceId") Long tDeviceId) {
        return success(tDeviceService.selectTDeviceByTDeviceId(tDeviceId));
    }

    /**
     * 新增设备管理
     */
    @ApiOperation(value="新增设备管理")
    @PreAuthorize("@ss.hasPermi('system:device:add')")
    @Log(title = "设备管理", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResult add(@RequestBody TDevice tDevice) {
        return toAjax(tDeviceService.insertTDevice(tDevice));
    }

    /**
     * 修改设备管理
     */
    @ApiOperation(value="修改设备管理")
    @PreAuthorize("@ss.hasPermi('system:device:edit')")
    @Log(title = "设备管理", businessType = BusinessType.UPDATE)
    @PutMapping("edit")
    public AjaxResult edit(@RequestBody TDevice tDevice) {
        return toAjax(tDeviceService.updateTDevice(tDevice));
    }

    /**
     * 删除设备管理
     */
    @ApiOperation(value="删除设备管理")
    @PreAuthorize("@ss.hasPermi('system:device:remove')")
    @Log(title = "设备管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{tDeviceIds}")
    public AjaxResult remove(@PathVariable Long[] tDeviceIds) {
        return toAjax(tDeviceService.deleteTDeviceByTDeviceIds(tDeviceIds));
    }
}
