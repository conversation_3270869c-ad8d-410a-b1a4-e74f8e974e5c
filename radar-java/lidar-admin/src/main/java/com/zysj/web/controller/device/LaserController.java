package com.zysj.web.controller.device;

import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.core.page.TableDataInfo;
import com.zysj.common.enums.BusinessType;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.system.domain.device.Laser;
import com.zysj.system.service.device.ILaserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 转台管理Controller
 * 
 * <AUTHOR>
 * @date 2025-02-09
 */
@RestController
@RequestMapping("/device/laser")
public class LaserController extends BaseController
{
    @Autowired
    private ILaserService laserService;

    /**
     * 获取激光监控数据
     */
    @PreAuthorize("@ss.hasPermi('system:sensor:query')")
    @GetMapping(value = "/getMonitorStatus")
    public AjaxResult getMonitorStatus()
    {
        return success(laserService.getMonitorStatus());
    }

    /**
     * 打开
     * 查询转台管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:laser:list')")
    @GetMapping("/list")
    public TableDataInfo list(Laser laser)
    {
        startPage();
        List<Laser> list = laserService.getList(laser);
        return getDataTable(list);
    }
    /**
     * 查询转台管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:laser:list')")
    @GetMapping("/listAll")
    public AjaxResult getAllList(Laser laser)
    {
        List<Laser> list = laserService.getList(laser);
        return success(list);
    }


    /**
     * 导出转台管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:laser:export')")
    @Log(title = "转台管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Laser laser)
    {
        List<Laser> list = laserService.getList(laser);
        ExcelUtil<Laser> util = new ExcelUtil<Laser>(Laser.class);
        util.exportExcel(response, list, "转台管理数据");
    }

    /**
     * 获取转台管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:laser:query')")
    @GetMapping(value = "/{laserId}")
    public AjaxResult getInfo(@PathVariable("laserId") Long laserId)
    {
        return success(laserService.getInfo(laserId));
    }

    /**
     * 新增转台管理
     */
    @PreAuthorize("@ss.hasPermi('system:laser:add')")
    @Log(title = "转台管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Laser laser)
    {
        return toAjax(laserService.insert(laser));
    }

    /**
     * 修改转台管理
     */
    @PreAuthorize("@ss.hasPermi('system:laser:edit')")
    @Log(title = "转台管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Laser laser)
    {
        return toAjax(laserService.update(laser));
    }

    /**
     * 删除转台管理
     */
    @PreAuthorize("@ss.hasPermi('system:laser:remove')")
    @Log(title = "转台管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{laserIds}")
    public AjaxResult remove(@PathVariable Long[] laserIds)
    {
        return toAjax(laserService.deleteByIds(laserIds));
    }
}
