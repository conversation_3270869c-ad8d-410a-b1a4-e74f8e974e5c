package com.zysj.web.controller.system;

import com.zysj.common.annotation.Log;
import com.zysj.common.core.controller.BaseController;
import com.zysj.common.core.domain.AjaxResult;
import com.zysj.common.core.page.TableDataInfo;
import com.zysj.common.enums.BusinessType;
import com.zysj.common.utils.poi.ExcelUtil;
import com.zysj.system.domain.TSensorDevice;
import com.zysj.system.service.ITSensorDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 传感器管理Controller
 *
 * <AUTHOR>
 * @date 2024-10-17
 */
@Api(tags="传感器管理")
@RestController
@RequestMapping("/system/sensorDevice")
public class TSensorDeviceController extends BaseController {
    @Autowired
    private ITSensorDeviceService tSensorDeviceService;

    /**
     * 查询传感器管理列表
     */
    @ApiOperation(value="查询传感器管理列表")
    @PreAuthorize("@ss.hasPermi('system:device:list')")
    @GetMapping("/list")
    public TableDataInfo list(TSensorDevice tSensorDevice) {
        startPage();
        List<TSensorDevice> list = tSensorDeviceService.selectTSensorDeviceList(tSensorDevice);
        return getDataTable(list);
    }

    /**
     * 导出传感器管理列表
     */
    @ApiOperation(value="导出传感器管理列表")
    @PreAuthorize("@ss.hasPermi('system:device:export')")
    @Log(title = "传感器管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TSensorDevice tSensorDevice) {
        List<TSensorDevice> list = tSensorDeviceService.selectTSensorDeviceList(tSensorDevice);
        ExcelUtil<TSensorDevice> util = new ExcelUtil<TSensorDevice>(TSensorDevice.class);
        util.exportExcel(response, list, "传感器管理数据");
    }

    /**
     * 获取传感器管理详细信息
     */
    @ApiOperation(value="获取传感器管理详细信息")
    @PreAuthorize("@ss.hasPermi('system:device:query')")
    @GetMapping(value = "/{tSensorDeviceId}")
    public AjaxResult getInfo(@PathVariable("tSensorDeviceId") Long tSensorDeviceId) {
        return success(tSensorDeviceService.selectTSensorDeviceByTSensorDeviceId(tSensorDeviceId));
    }

    /**
     * 新增传感器管理
     */
    @ApiOperation(value="新增传感器管理")
    @PreAuthorize("@ss.hasPermi('system:device:add')")
    @Log(title = "传感器管理", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResult add(@RequestBody TSensorDevice tSensorDevice) {
        return toAjax(tSensorDeviceService.insertTSensorDevice(tSensorDevice));
    }

    /**
     * 修改传感器管理
     */
    @ApiOperation(value="修改传感器管理")
    @PreAuthorize("@ss.hasPermi('system:device:edit')")
    @Log(title = "传感器管理", businessType = BusinessType.UPDATE)
    @PutMapping("edit")
    public AjaxResult edit(@RequestBody TSensorDevice tSensorDevice) {
        return toAjax(tSensorDeviceService.updateTSensorDevice(tSensorDevice));
    }

    /**
     * 删除传感器管理
     */
    @ApiOperation(value="删除传感器管理")
    @PreAuthorize("@ss.hasPermi('system:device:remove')")
    @Log(title = "传感器管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{tSensorDeviceIds}")
    public AjaxResult remove(@PathVariable Long[] tSensorDeviceIds) {
        return toAjax(tSensorDeviceService.deleteTSensorDeviceByTSensorDeviceIds(tSensorDeviceIds));
    }
}
