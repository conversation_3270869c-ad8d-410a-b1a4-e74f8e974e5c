package com.zysj.web.controller.file;

import com.zysj.common.utils.MinioUtil;
import com.zysj.system.service.file.MinioFilesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;


@RestController
@CrossOrigin
@RequestMapping("/file/minio")
public class MinioController {

    @Autowired
    private MinioUtil minioUtil;
    @Resource
    private MinioFilesService minioFilesService;

    /**
     * 上传文件
     */
    @PostMapping(value = "/upload")
    public String uploadReport(MultipartFile file) {
        // 获取到上传的文件名
        String fileName = file.getOriginalFilename();

        // 上传文件
        minioUtil.upload(file, fileName);
        String fileUrl = minioUtil.getFileUrl(fileName);
        if (Objects.isNull(fileUrl)) throw new RuntimeException("上传失败");
        // 上传成功后将文件信息保存到数据库中
        minioFilesService.doAfterUpload(file);
        // 获取上传的文件地址
        return fileUrl;
    }

    /**
     * 预览文件
     */
    @GetMapping("/preview")
    public String preview(String fileName) {
        return minioUtil.getFileUrl(fileName);
    }

    /**
     * 下载文件
     */
    @GetMapping("/download")
    public void download(String fileName, HttpServletResponse response) {
        minioUtil.download(response, fileName);
    }

    /**
     * 删除文件
     */
    @GetMapping("/delete")
    public String delete(String fileName) {
        minioUtil.delete(fileName);
        return "删除成功";
    }

}