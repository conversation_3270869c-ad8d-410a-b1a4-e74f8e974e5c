package com.zysj.common.dll;

public class MyLibrary {
    static {
        // 获取项目根目录的路径，并加载 DLL 文件
        String dllPath = "E:\\lidar-backend\\src\\main\\java\\com\\radar\\project\\dll\\MyLibrary.dll";
        System.load(dllPath);
    }
//    static {
//        System.loadLibrary("MyLibrary");
//    }

    public native int add(int a, int b);

    public static void main(String[] args) {
        MyLibrary lib = new MyLibrary();
        int result = lib.add(2, 3);
        System.out.println("Result: " + result);
    }
}

