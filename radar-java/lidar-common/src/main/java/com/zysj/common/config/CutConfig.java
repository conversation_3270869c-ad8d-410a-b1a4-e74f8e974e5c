package com.zysj.common.config;

import lombok.Data;

@Data
public class CutConfig {
    private byte processMode;
    private byte stepScan;
    private float azimuth;
    private float elevation;
    private float azStart;
    private float elStart;
    private float azEnd;
    private float elEnd;
    private float angularResolution;
    private float azimuthScanSpeed;
    private float elevationScanSpeed;
    private byte scanDirection;
    private short rangeResolution;
    private int maxRange;
    private short startRange;
    private short sample;
    private float maxMeasurementSpeed;
    private byte dopplerReference;
    private float cnrThreshold;
    private byte rangeResolutionUniformity;
    private byte operatingMode;

    public CutConfig(int processMode, float azimuth, float elevation, float azStart, float elStart, float azEnd, float elEnd, float angularResolution, float azimuthScanSpeed, float elevationScanSpeed, int scanDirection, int rangeResolution, int maxRange, int startRange, int sample, float maxMeasurementSpeed, int dopplerReference, float cnrThreshold, int rangeResolutionUniformity, int operatingMode) {
        this.processMode = (byte) processMode;
        this.stepScan = 1; // 连续扫描
        this.azimuth = azimuth;
        this.elevation = elevation;
        this.azStart = azStart;
        this.elStart = elStart;
        this.azEnd = azEnd;
        this.elEnd = elEnd;
        this.angularResolution = angularResolution;
        this.azimuthScanSpeed = azimuthScanSpeed;
        this.elevationScanSpeed = elevationScanSpeed;
        this.scanDirection = (byte) scanDirection;
        this.rangeResolution = (short) rangeResolution;
        this.maxRange = maxRange;
        this.startRange = (short) startRange;
        this.sample = (short) sample;
        this.maxMeasurementSpeed = maxMeasurementSpeed;
        this.dopplerReference = (byte) dopplerReference;
        this.cnrThreshold = cnrThreshold;
        this.rangeResolutionUniformity = (byte) rangeResolutionUniformity;
        this.operatingMode = (byte) operatingMode;
    }

    public byte getProcessMode() {
        return processMode;
    }

    public byte getStepScan() {
        return stepScan;
    }

    public float getAzimuth() {
        return azimuth;
    }

    public float getElevation() {
        return elevation;
    }

    public float getAzStart() {
        return azStart;
    }

    public float getElStart() {
        return elStart;
    }

    public float getAzEnd() {
        return azEnd;
    }

    public float getElEnd() {
        return elEnd;
    }

    public float getAngularResolution() {
        return angularResolution;
    }

    public float getAzimuthScanSpeed() {
        return azimuthScanSpeed;
    }

    public float getElevationScanSpeed() {
        return elevationScanSpeed;
    }

    public byte getScanDirection() {
        return scanDirection;
    }

    public short getRangeResolution() {
        return rangeResolution;
    }

    public int getMaxRange() {
        return maxRange;
    }

    public short getStartRange() {
        return startRange;
    }

    public short getSample() {
        return sample;
    }

    public float getMaxMeasurementSpeed() {
        return maxMeasurementSpeed;
    }

    public byte getDopplerReference() {
        return dopplerReference;
    }

    public float getCnrThreshold() {
        return cnrThreshold;
    }

    public byte getRangeResolutionUniformity() {
        return rangeResolutionUniformity;
    }

    public byte getOperatingMode() {
        return operatingMode;
    }
}
