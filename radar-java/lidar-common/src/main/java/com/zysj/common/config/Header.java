package com.zysj.common.config;

import lombok.Data;

@Data
public class Header {
    private int magicNumber = 0x4344574C; // CDWL
    private int majorVersion = 1;
    private int minorVersion = 0;
    private int genericType = 1;
    private int productCode = 0;
    private byte[] reserved = new byte[28];

    public int getMagicNumber() {
        return magicNumber;
    }

    public int getMajorVersion() {
        return majorVersion;
    }

    public int getMinorVersion() {
        return minorVersion;
    }

    public int getGenericType() {
        return genericType;
    }

    public int getProductCode() {
        return productCode;
    }

    public byte[] getReserved() {
        return reserved;
    }
}
