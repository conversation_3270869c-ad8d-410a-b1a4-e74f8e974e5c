package com.zysj.common.annotation;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD) // 注解用于字段
@Retention(RetentionPolicy.RUNTIME) // 注解在运行时保留
public @interface CustomAnnotation {
    int length() default 0; // 长度
    int index() default 0; // 下标位置
    String type() default ""; // 数据类型
}