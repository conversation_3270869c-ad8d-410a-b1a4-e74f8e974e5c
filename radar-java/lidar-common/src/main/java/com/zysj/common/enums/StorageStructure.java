package com.zysj.common.enums;

public enum StorageStructure {
    RADIAL(0),
    RASTER(1),
    TABLE(2);

    private int value;

    StorageStructure(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    // 根据值获取对应的枚举实例
    public static StorageStructure getByValue(int value) {
        for (StorageStructure storageStructure: StorageStructure.values()) {
            if (storageStructure.getValue() == value) {
                return storageStructure;
            }
        }
        return null; // 或者抛出一个异常，如果你确定这个值总是有效的
    }
}
