/*
 Lidar Database Data Insertion Script
 
 This file contains INSERT statements for initial data in the lidar database.
 Run this script after running lidar_init.sql to populate the database with sample data.
 
 Date: 2025-09-02
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Records of capture_card
-- ----------------------------
INSERT INTO `capture_card` VALUES (1, NULL, '183', '89999', '采集', '222222222221.0035ttt', '22.00', '2024-10-31 00:00:00', '233', NULL, NULL, NULL, 'admin', '2025-02-20 17:01:38', NULL, NULL, '88888');

-- ----------------------------
-- Records of compass
-- ----------------------------
INSERT INTO `compass` VALUES (41, 12, NULL, 'admin', '2025-02-09 14:44:54', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Records of data_receive_set
-- ----------------------------
INSERT INTO `data_receive_set` VALUES (1, '1', NULL, NULL, NULL, '5666666666', '6', NULL, '2', '3', '4', NULL, NULL, NULL, NULL, '2024-10-23 18:22:58', NULL);

-- ----------------------------
-- Records of data_upload_set
-- ----------------------------
INSERT INTO `data_upload_set` VALUES (1, NULL, '0', NULL, '8', NULL, NULL, '9', '10', '11', NULL, NULL, '12', NULL, NULL, NULL, NULL, '2024-10-22 18:02:00', NULL);

-- ----------------------------
-- Records of data_warn_log
-- ----------------------------
INSERT INTO `data_warn_log` VALUES (2, NULL, '111', '2', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-10-22 17:26:22', NULL, '2024-10-22 17:27:13', '2222');
INSERT INTO `data_warn_log` VALUES (3, NULL, '22', '1', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-10-22 17:26:28', NULL, NULL, '333');

-- ----------------------------
-- Records of data_warn_set
-- ----------------------------
INSERT INTO `data_warn_set` VALUES (4, NULL, '预警名称一', '0', '0', '1', NULL, 50, 20, NULL, NULL, '2024-10-22 17:23:55', NULL, NULL, '预警提示预警提示预警提示');

-- ----------------------------
-- Records of device
-- ----------------------------
INSERT INTO `device` VALUES (1, '1t', '2tttttttttt', '3ttttttttt', '3.5ttttttt', '1', '0', '0', NULL, '2024-10-21 18:37:22', NULL, '2024-10-23 18:17:59', NULL);

-- ----------------------------
-- Records of device_warn_log
-- ----------------------------
INSERT INTO `device_warn_log` VALUES (10712, NULL, 2, '2', '系统电压', '23.114', 35.0, NULL, NULL, NULL, NULL, '2025-07-10 13:52:01', NULL, NULL, 'sss', NULL);

-- ----------------------------
-- Records of device_warn_set
-- ----------------------------
INSERT INTO `device_warn_set` VALUES (18, 2, NULL, NULL, '5', NULL, '0', 50, NULL, NULL, 'admin', '2025-03-11 16:10:43', NULL, NULL, '1111111', NULL);
INSERT INTO `device_warn_set` VALUES (20, 2, NULL, NULL, '3', NULL, '2', 35, NULL, NULL, 'admin', '2025-03-11 17:06:50', NULL, NULL, 'sss', NULL);
INSERT INTO `device_warn_set` VALUES (21, 2, NULL, NULL, '8', NULL, '0', 25, NULL, NULL, 'admin', '2025-03-11 17:07:01', NULL, NULL, 'ssssaaaaaa', NULL);

-- ----------------------------
-- Records of file_config
-- ----------------------------
INSERT INTO `file_config` VALUES (6, 'SYSTEM', 0, 0, 30, 0, '2025-06-16 14:44:32');
INSERT INTO `file_config` VALUES (7, 'IPC', 0, 0, 30, 0, '2025-06-16 14:44:43');

-- ----------------------------
-- Records of gen_table
-- ----------------------------
INSERT INTO `gen_table` VALUES (119, 'data_warn_log', '采集数据预警日志', NULL, NULL, 'DataWarnLog', 'crud', 'element-plus', 'com.zysj.system', 'system', 'dataWarnLog', '采集数据预警日志', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:38:17', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (120, 'data_warn_set', '采集数据预警设置', NULL, NULL, 'DataWarnSet', 'crud', 'element-plus', 'com.zysj.system', 'system', 'dataWarnSet', '采集数据预警设置', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:38:20', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (121, 'device_warn_log', '设备预警日志', NULL, NULL, 'DeviceWarnLog', 'crud', 'element-plus', 'com.zysj.system', 'system', 'deviceWarnLog', '设备预警日志', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:38:22', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (124, 'scan_element_type', '', NULL, NULL, 'ScanElementType', 'crud', 'element-plus', 'com.zysj.system', 'system', 'scanElementType', NULL, 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:38:29', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (125, 'system_alarm_category', '', NULL, NULL, 'SystemAlarmCategory', 'crud', 'element-plus', 'com.zysj.system', 'system', 'systemAlarmCategory', NULL, 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:38:31', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (126, 'system_alarm_record', '设备预警日志', NULL, NULL, 'SystemAlarmRecord', 'crud', 'element-plus', 'com.zysj.system', 'system', 'systemAlarmRecord', '设备预警日志', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:38:32', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (127, 'system_alarm_set', '', NULL, NULL, 'SystemAlarmSet', 'crud', 'element-plus', 'com.zysj.system', 'system', 'systemAlarmSet', NULL, 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:38:34', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (128, 'th_sensor', '传感器管理', NULL, NULL, 'ThSensor', 'crud', 'element-plus', 'com.zysj.system', 'system', 'thSensor', '传感器管理', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:38:36', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (129, 'data_receive_set', '数据接收设置', NULL, NULL, 'DataReceiveSet', 'crud', 'element-plus', 'com.zysj.system', 'system', 'dataReceiveSet', '数据接收设置', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:38:56', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (130, 'data_storage_set', '数据存储设置', NULL, NULL, 'DataStorageSet', 'crud', 'element-plus', 'com.zysj.system', 'system', 'dataStorageSet', '数据存储设置', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:38:58', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (131, 'data_upload_set', '数据上传设置', NULL, NULL, 'DataUploadSet', 'crud', 'element-plus', 'com.zysj.system', 'system', 'dataUploadSet', '数据上传设置', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:39:00', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (132, 'mainten_record', '维修管理', NULL, NULL, 'MaintenRecord', 'crud', 'element-plus', 'com.zysj.system', 'system', 'maintenRecord', '维修管理', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:39:03', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (133, 'mainten_record_detail', '维修明细', NULL, NULL, 'MaintenRecordDetail', 'crud', 'element-plus', 'com.zysj.system', 'system', 'maintenRecordDetail', '维修明细', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:39:06', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (134, 'product_type', '', NULL, NULL, 'ProductType', 'crud', 'element-plus', 'com.zysj.system', 'system', 'productType', NULL, 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:39:09', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (135, 'radar_file', '文件管理', NULL, NULL, 'RadarFile', 'crud', 'element-plus', 'com.zysj.system', 'system', 'radarFile', '文件管理', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:39:10', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (136, 'radial_data_state', '', NULL, NULL, 'RadialDataState', 'crud', 'element-plus', 'com.zysj.system', 'system', 'radialDataState', NULL, 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:39:13', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (137, 'scan_type', '', NULL, NULL, 'ScanType', 'crud', 'element-plus', 'com.zysj.system', 'system', 'scanType', NULL, 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:39:15', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (138, 'software', '软件管理', NULL, NULL, 'Software', 'crud', 'element-plus', 'com.zysj.system', 'system', 'software', '软件管理', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:39:17', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (139, 'capture_card', '采集卡管理', NULL, NULL, 'CaptureCard', 'crud', 'element-plus', 'com.zysj.system', 'system', 'captureCard', '采集卡管理', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:40:10', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (140, 'compass', '指北针修正管理', NULL, NULL, 'Compass', 'crud', 'element-plus', 'com.zysj.system', 'system', 'compass', '指北针修正管理', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:40:12', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (141, 'laser_param', '工控机激光参数管理', NULL, NULL, 'LaserParam', 'crud', 'element-plus', 'com.zysj.system', 'system', 'laserParam', '工控机激光参数管理', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:40:13', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (142, 'radar_ex', '', NULL, NULL, 'RadarEx', 'crud', 'element-plus', 'com.zysj.system', 'system', 'radarEx', NULL, 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:40:16', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (144, 'radar_movement', '子任务运动轨迹', NULL, NULL, 'RadarMovement', 'crud', 'element-plus', 'com.zysj.system', 'system', 'radarMovement', '子任务运动轨迹', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:40:20', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (145, 'scan_scheme', '任务参数配置', NULL, NULL, 'ScanScheme', 'crud', 'element-plus', 'com.zysj.system', 'system', 'scanScheme', '任务参数配置', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:40:22', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (146, 'site', '站点管理', NULL, NULL, 'Site', 'crud', 'element-plus', 'com.zysj.system', 'system', 'site', '站点管理', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:40:26', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (147, 'turntable', '转台管理', NULL, NULL, 'Turntable', 'crud', 'element-plus', 'com.zysj.system', 'system', 'turntable', '转台管理', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:40:28', '', NULL, NULL);
INSERT INTO `gen_table` VALUES (148, 'turntable_param', '工控机转台参数管理', NULL, NULL, 'TurntableParam', 'crud', 'element-plus', 'com.zysj.system', 'system', 'turntableParam', '工控机转台参数管理', 'zysj', '0', '/', NULL, 'admin', '2025-02-04 14:40:30', '', NULL, NULL);

SET FOREIGN_KEY_CHECKS = 1;
