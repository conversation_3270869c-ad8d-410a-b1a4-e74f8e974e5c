package org.jeecg.modules.data.entity;

import org.jeecg.common.system.redis.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.Data;

/**
 * 全局数据实体类
 * 
 * <AUTHOR>
 * @date 2025-01-26
 */
@Data
@Component
public class GlobalData {

    @Autowired
    private RedisUtil redisUtil;

    private String SCAN_KEY = "ppi:scan:global";

    //转台监控数据
    private TurntableMonitor turntableMonitor;
    
    //温湿度数据
    private TemperatureHumidity temperatureHumidity;

    //激光器监控数据
    private LaserMonitor laserMonitor;

    //频谱数据
    private FreqSpecData freqSpecData;

    //系统汇总信息
    private SysSumInfo sysSumInfo;

    // 当前扫描任务id
    private String scanTaskId;

    // 当前扫描子任务id
    private String scanSubTaskId;

    public void setScanSubTaskId(String scanSubTaskId) {
        //这里如果子任务切换了，则清空ppi的缓存数据
        if (this.scanSubTaskId != null && !this.scanSubTaskId.equals(scanSubTaskId)) {
            redisUtil.del(SCAN_KEY);
        }
        this.scanSubTaskId = scanSubTaskId;
    }
}
