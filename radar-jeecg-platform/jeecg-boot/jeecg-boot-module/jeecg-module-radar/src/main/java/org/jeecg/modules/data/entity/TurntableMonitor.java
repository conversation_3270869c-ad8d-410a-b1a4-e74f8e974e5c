package org.jeecg.modules.data.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 转台监控数据实体类
 * 
 * <AUTHOR>
 * @date 2025-01-26
 */
@Data
@NoArgsConstructor
public class TurntableMonitor {

    /**
     * 转台状态
     */
    private String status;
    
    /**
     * 当前方位角
     */
    private Double azimuthAngle;
    
    /**
     * 当前俯仰角
     */
    private Double elevationAngle;
    
    /**
     * 转台温度
     */
    private Double temperature;
    
    /**
     * 转台电压
     */
    private Double voltage;
    
    /**
     * 转台电流
     */
    private Double current;
    
    /**
     * 转台转速
     */
    private Double rotationSpeed;
    
    /**
     * 是否在线
     */
    private Boolean online;
    
    /**
     * 最后更新时间
     */
    private String lastUpdateTime;
}
