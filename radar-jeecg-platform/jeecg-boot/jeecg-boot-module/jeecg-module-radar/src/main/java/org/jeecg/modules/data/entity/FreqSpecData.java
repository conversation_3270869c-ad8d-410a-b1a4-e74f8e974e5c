package org.jeecg.modules.data.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 频谱数据实体类
 * 
 * <AUTHOR>
 * @date 2025-01-26
 */
@Data
@NoArgsConstructor
public class FreqSpecData {

    /**
     * 门长度（默认参数200）
     */
    private int samplesPerGate;
    
    /**
     * 激光脉冲重复周期，MSB=2.5ns
     */
    private int prtLen;
    
    /**
     * FFT处理起始位置，MSB=2.5ns，0≤OffsetNo≤4000
     */
    private int fftStart;
    
    /**
     * 脉冲积累次数，1000≤PulsesNo≤65535
     */
    private int pulsesNumber;
    
    /**
     * 距离门数（67/40）
     */
    private int gateNum;
    
    /**
     * FFT点数（256/512）
     */
    private int fftLen;

    /**
     * 窗函数长度
     */
    private int windowFunLen;

    /**
     * 脉宽 关键字
     */
    private int pulseWidth;

    /**
     * 频率
     */
    private int frequency;

    /**
     * 功率
     */
    private int power;
    
    /**
     * AOM频移
     */
    private float aomFreqShift;

    /**
     * 原始频率
     */
    private int freqNum;
    
    /**
     * 方位角
     */
    private float azimuthAngle;

    /**
     * 倾斜角
     */
    private float elevationAngle;

    /**
     * 频率数组
     */
    private double[] freq;

    /**
     * 回波数据
     */
    private double[] spec;
}
